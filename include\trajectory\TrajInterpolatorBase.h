#ifndef TRAJ_INTERPOLATOR_BASE_H
#define TRAJ_INTERPOLATOR_BASE_H

#include <Eigen/Dense>
#include <vector>
#include <string>

// 抽象轨迹插补接口
// N: 关节数
// VectorNd/MatrixNd: Eigen 类型

template <const int N>
class TrajInterpolator {
public:
    using VectorNd = Eigen::Matrix<double, N, 1>;
    using MatrixNd = Eigen::Matrix<double, N, Eigen::Dynamic>;

    TrajInterpolator(double control_period, const std::string& urdf_path) : m_dt(control_period), m_urdf_path(urdf_path) {}
    virtual ~TrajInterpolator() = default;

    virtual void setConstraint(const VectorNd& max_vel,
                               const VectorNd& max_acc,
                               const VectorNd& max_jerk) {
        this->m_max_vel = max_vel;
        this->m_max_acc = max_acc;
        this->m_max_jerk = max_jerk;
    }

    // 离线计算接口（仅对离线库如TOPP-RA有效，在线库空实现）
    virtual void computeOffline(const std::vector<VectorNd>& waypoints) = 0;

    // 单步在线更新接口（仅对在线库如Ruckig有效，离线库空实现）
    virtual bool computeOnline(const VectorNd& current_position,
                               const VectorNd& current_velocity,
                               const VectorNd& current_acceleration,
                               const VectorNd& target_position,
                               const VectorNd& target_velocity,
                               const VectorNd& target_acceleration,
                               double time_step) {
        return false;
    }

    // 采样轨迹：按时间间隔采样，返回时间戳、位置、速度、加速度
    virtual void sample(double dt,
                        std::vector<double>& ts,
                        MatrixNd& pos,
                        MatrixNd& vel,
                        MatrixNd& acc) const = 0;

protected:
    VectorNd m_max_vel = VectorNd::Constant(2.0);   // 最大速度
    VectorNd m_max_acc = VectorNd::Constant(10.0);  // 最大加速度
    VectorNd m_max_jerk = VectorNd::Constant(20.0); // 最大加加速度（jerk）   
    double m_dt;                                    // 控制周期
    std::string m_urdf_path;                        // URDF路径
};

#endif // TRAJ_INTERPOLATOR_BASE_H 