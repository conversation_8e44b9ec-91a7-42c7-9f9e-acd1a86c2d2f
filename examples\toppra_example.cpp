#include <iostream>
#include <vector>
#include <iomanip> // For std::setprecision

<<<<<<< HEAD
#include "../include/trajectory/ToppraInterpolator.hpp"
=======
<<<<<<< HEAD:examples/toppra_example.cpp
#include "../include/trajectory/ToppraInterpolator.hpp"
=======
#include "ToppraInterpolator.h"
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0:example/toppra_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

constexpr int DOF = 3;

int main() {
<<<<<<< HEAD
=======
<<<<<<< HEAD:examples/toppra_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

    // Create ToppraInterpolator with new interface
    ToppraInterpolator<DOF> interpolator(0.01, "");

    // Set motion constraints using new MotionConstraints structure
    MotionConstraints<DOF> constraints;
    constraints.max_velocity = VectorDOF::Constant(1.0);
    constraints.max_acceleration = VectorDOF::Constant(2.0);
    constraints.max_jerk = VectorDOF::Constant(5.0);
    interpolator.setConstraints(constraints);

    // Create waypoints using new MotionState structure
    std::vector<MotionState<DOF>> waypoints;

    // Start waypoint
    MotionState<DOF> start_state;
    start_state.position << 0.0, 0.0, 0.0;
    start_state.velocity << 0.0, 0.0, 0.0;
    start_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(start_state);

    // Intermediate waypoint
    MotionState<DOF> mid_state;
    mid_state.position << 1.0, 0.5, -0.2;
    mid_state.velocity << 0.0, 0.0, 0.0;
    mid_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(mid_state);

    // End waypoint
    MotionState<DOF> end_state;
    end_state.position << 2.0, 0.0, 0.0;
    end_state.velocity << 0.0, 0.0, 0.0;
    end_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(end_state);

    // Compute trajectory using new interface (offline planning)
    bool success = interpolator.computeOffline(waypoints);

    if (!success) {
        std::cerr << "Failed to compute trajectory: " << interpolator.getLastError() << std::endl;
        return -1;
    }

    std::cout << "Trajectory computation successful!" << std::endl;
    std::cout << "Interpolator type: " << interpolator.getTypeName() << std::endl;
    std::cout << "Trajectory duration: " << interpolator.getDuration() << " seconds" << std::endl;


    // Get trajectory buffer and sample at regular intervals
    const auto& trajectory_buffer = interpolator.getTrajectoryBuffer();
    double dt = 0.02;
    double duration = interpolator.getDuration();
    int num_samples = static_cast<int>(duration / dt) + 1;

    // Set output precision
    std::cout << std::fixed << std::setprecision(4);
    std::cout << "\nTrajectory samples using getTrajectoryBuffer():" << std::endl;
    std::cout << "Buffer size: " << trajectory_buffer.size() << " states" << std::endl;

    // Sample trajectory using the TrajectoryBuffer (show first 10 and last 10)
    for (int i = 0; i < num_samples; ++i) {
        // Only print first 10 and last 10 samples to avoid too much output
        if (i < 10 || i >= num_samples - 10) {
            double t = std::min(i * dt, duration);
            TrajectoryState<DOF> state = trajectory_buffer.getStateAtTime(t);

            if (state.valid) {
                std::cout << "t=" << t
                          << ", pos=[" << state.position.transpose() << "]"
                          << ", vel=[" << state.velocity.transpose() << "]"
                          << ", acc=[" << state.acceleration.transpose() << "]" << std::endl;
            }
        } else if (i == 10) {
            std::cout << "... (skipping intermediate samples) ..." << std::endl;
        }
    }

    return 0;
<<<<<<< HEAD
}
=======
}
=======
    using VectorNd = Eigen::Matrix<double, DOF, 1>;
    using MatrixNd = Eigen::Matrix<double, DOF, Eigen::Dynamic>;

    ToppraInterpolator<DOF> interpolator(0.01, "");

    VectorNd max_vel = VectorNd::Constant(1.0);
    VectorNd max_acc = VectorNd::Constant(2.0);
    VectorNd max_jerk = VectorNd::Constant(5.0);

    interpolator.setConstraint(max_vel, max_acc, max_jerk);

    std::vector<VectorNd> waypoints;
    waypoints.push_back((VectorNd() << 0.0, 0.0, 0.0).finished());
    waypoints.push_back((VectorNd() << 1.0, 0.5, -0.2).finished());
    waypoints.push_back((VectorNd() << 2.0, 0.0, 0.0).finished());

    interpolator.computeOffline(waypoints);

    std::vector<double> ts;
    MatrixNd pos, vel, acc;
    interpolator.sample(0.02, ts, pos, vel, acc);

    // 设置输出精度
    std::cout << std::fixed << std::setprecision(4);

    // 打印采样结果
    for (size_t i = 0; i < ts.size(); ++i) {
        std::cout << "t=" << ts[i] << ", pos=[" << pos.row(i) << "], vel=[" << vel.row(i) << "], acc=[" << acc.row(i) << "]\n";
    }
    return 0;
} 
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0:example/toppra_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
