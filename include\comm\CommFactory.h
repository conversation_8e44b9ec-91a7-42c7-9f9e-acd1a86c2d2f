#ifndef COMM_FACTORY_H
#define COMM_FACTORY_H

#include "CommBase.h"
#include "ZMQComm.h"
<<<<<<< HEAD
=======
<<<<<<< HEAD
=======
#include "FIFOComm.h"
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
#include <memory>

class CommFactory {
public:
    static std::unique_ptr<CommBase> createComm(CommType type, bool is_server = false) {
        switch (type) {
            case CommType::ZMQ:
                return std::make_unique<ZMQComm>(is_server);
<<<<<<< HEAD
=======
<<<<<<< HEAD
=======
            case CommType::FIFO:
                return std::make_unique<FIFOComm>(is_server);
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            default:
                return nullptr;
        }
    }
};

#endif // COMM_FACTORY_H 