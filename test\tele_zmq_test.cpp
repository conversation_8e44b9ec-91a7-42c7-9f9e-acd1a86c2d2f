#include <zmq.h>
#include <iostream>
#include <cstring>
#include <chrono>
#include <thread>
#include <signal.h>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <functional>
#include "industRob.h"

// 原始二进制数据结构，与Python端发送的数据格式对应
struct JoystickRawData {
    double x, y, z, rx, ry, rz;  // 位姿数据
    int start, suck, reset, record;  // 按钮状态
} __attribute__((packed));

// 手柄数据结构
struct JoystickData {
    double pose[6];  // x, y, z, rx, ry, rz
    int start;       // start按钮状态 (0/1)
    int suck;        // suck按钮状态 (0/1)
    int reset;       // reset按钮状态 (0/1)
    int record;      // record状态 (0=不记录, 1=普通, 2=记录中)
    bool valid;      // 数据有效性标志
    
    JoystickData() : start(0), suck(0), reset(0), record(0), valid(false) {
        for (int i = 0; i < 6; i++) pose[i] = 0.0;
    }
};

// 全局标志用于优雅退出
volatile bool running = true;

void signalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", exiting..." << std::endl;
    running = false;
}

// 运动数据处理器类 - 负责队列管理和伺服控制
class MotionDataProcessor {
private:
    std::queue<std::vector<double>> motion_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::thread worker_thread_;
    bool running_;
    industRob* robot_;
    static constexpr size_t MAX_QUEUE_SIZE = 5;  // 最大队列长度

public:
    MotionDataProcessor(industRob* robot) : running_(false), robot_(robot) {}

    ~MotionDataProcessor() {
        stop();
    }

    void start() {
        running_ = true;
        worker_thread_ = std::thread(&MotionDataProcessor::workerLoop, this);
        std::cout << "[MotionProcessor] Started worker thread" << std::endl;
    }

    void stop() {
        if (running_) {
            running_ = false;
            queue_cv_.notify_all();
            if (worker_thread_.joinable()) {
                worker_thread_.join();
            }
            std::cout << "[MotionProcessor] Stopped worker thread" << std::endl;
        }
    }

    void pushMotionData(const std::vector<double>& pose) {
        std::lock_guard<std::mutex> lock(queue_mutex_);

        // 如果队列已满，移除最旧的数据
        while (motion_queue_.size() >= MAX_QUEUE_SIZE) {
            motion_queue_.pop();
        }

        motion_queue_.push(pose);
        queue_cv_.notify_one();

        // 可选：记录队列状态（用于调试）
        static int push_count = 0;
        if (++push_count % 50 == 0) {  // 每50次推送打印一次
            std::cout << "[MotionProcessor] Queue size: " << motion_queue_.size()
                      << "/" << MAX_QUEUE_SIZE << " (push count: " << push_count << ")" << std::endl;
        }
    }

private:
    void workerLoop() {
        std::cout << "[MotionProcessor] Worker thread started" << std::endl;
        double time_stamp = 0.016;
        // 固定周期参数设置
        const std::chrono::microseconds cycle_time(16000);  // 20ms = 50Hz 控制周期
        std::chrono::steady_clock::time_point next_cycle_time;
        std::chrono::steady_clock::time_point cycle_start_time;
        int servo_count = 0;

        // 初始化下一个周期时间
        next_cycle_time = std::chrono::steady_clock::now();

        while (running_) {
            // 记录当前周期开始时间
            cycle_start_time = std::chrono::steady_clock::now();

            // 计算下一个周期的时间点
            next_cycle_time += cycle_time;

            // 获取最新的运动数据
            std::vector<double> pose;
            bool has_data = false;

            {
                std::unique_lock<std::mutex> lock(queue_mutex_);

                // 非阻塞检查队列
                if (!motion_queue_.empty()) {
                    pose = motion_queue_.front();
                    motion_queue_.pop();
                    has_data = true;

                    // 如果队列中有多个数据，只保留最新的一个
                    while (motion_queue_.size() > 1) {
                        motion_queue_.pop();
                    }
                }
            }
            static int servo_count = 0;
            static int64_t prev_ts = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count();

            // 如果有数据，执行伺服控制
            if (has_data) {
                try {
                    std::cout<<"time_stamp: "<<time_stamp<<std::endl;
                    // 执行实时伺服控制
                    robot_->servoL(pose, time_stamp, 0.1, 0.3);
                    time_stamp =time_stamp + 0.016;  // 更新周期时间
                    auto now_us = std::chrono::duration_cast<std::chrono::microseconds>(
                        std::chrono::steady_clock::now().time_since_epoch()).count();
            
                    int64_t now_ms = now_us / 1000;
                    int64_t delta_ms = now_ms - prev_ts;
                    prev_ts = now_ms;
            
                    std::cout << " time_stamp(ms): " << now_ms << "           delta: " << delta_ms << " ms" << std::endl;
            
                    if (++servo_count % 25 == 0) {
                        std::cout << "[MotionProcessor] servoL active (count: "
                                  << servo_count << ", queue size: "
                                  << motion_queue_.size() << ")" << std::endl;
                    }
                } catch (const std::exception& e) {
                    std::cerr << "[MotionProcessor] Error executing servoL: " << e.what() << std::endl;
                }
            }

            // 计算剩余时间并精确等待到下一个周期
            auto now = std::chrono::steady_clock::now();
            if (now < next_cycle_time) {
                std::this_thread::sleep_until(next_cycle_time);
            } else {
                // 如果已经超过了下一个周期时间，重新同步
                auto cycles_behind = std::chrono::duration_cast<std::chrono::microseconds>(now - next_cycle_time).count() / cycle_time.count() + 1;
                next_cycle_time += cycle_time * cycles_behind;
                std::cerr << "[MotionProcessor] Warning: Cycle time overrun by "
                          << cycles_behind << " cycles" << std::endl;
            }

            // 计算实际周期时间并记录
            auto actual_cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
                std::chrono::steady_clock::now() - cycle_start_time).count();

            // 每100个周期打印一次实际周期时间
            if (servo_count % 100 == 0) {
                std::cout << "[MotionProcessor] Actual cycle time: "
                          << actual_cycle_time << " us (target: "
                          << cycle_time.count() << " us)" << std::endl;
            }
        }

        std::cout << "[MotionProcessor] Worker thread finished" << std::endl;
    }
};

class SimpleZmqReceiver {
private:
    void* context_;
    void* socket_;
    bool connected_;
    bool running_;
    std::thread receiver_thread_;
    std::function<void(const JoystickData&)> data_callback_;

public:
    SimpleZmqReceiver() : context_(nullptr), socket_(nullptr), connected_(false), running_(false) {}

    ~SimpleZmqReceiver() {
        stop();
        disconnect();
    }

    // 注册回调函数
    void registerCallback(const std::function<void(const JoystickData&)>& callback) {
        data_callback_ = callback;
        std::cout << "[ZmqReceiver] Callback registered" << std::endl;
    }

    bool connect() {
        try {
            context_ = zmq_ctx_new();
            if (!context_) {
                std::cerr << "[ZmqReceiver] Failed to create ZMQ context" << std::endl;
                return false;
            }

            socket_ = zmq_socket(context_, ZMQ_SUB);
            if (!socket_) {
                std::cerr << "[ZmqReceiver] Failed to create ZMQ socket" << std::endl;
                zmq_ctx_destroy(context_);
                context_ = nullptr;
                return false;
            }

            // 连接到发布者
            int rc = zmq_connect(socket_, "ipc:///tmp/joystick_control");
            if (rc != 0) {
                std::cerr << "[ZmqReceiver] Failed to connect: " << zmq_strerror(errno) << std::endl;
                zmq_close(socket_);
                zmq_ctx_destroy(context_);
                socket_ = nullptr;
                context_ = nullptr;
                return false;
            }

            // 订阅主题
            rc = zmq_setsockopt(socket_, ZMQ_SUBSCRIBE, "joystick_data", 12);
            if (rc != 0) {
                std::cerr << "[ZmqReceiver] Failed to subscribe: " << zmq_strerror(errno) << std::endl;
                zmq_close(socket_);
                zmq_ctx_destroy(context_);
                socket_ = nullptr;
                context_ = nullptr;
                return false;
            }

            // 设置非阻塞模式
            int timeout = 0;
            zmq_setsockopt(socket_, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));

            connected_ = true;
            std::cout << "[ZmqReceiver] Connected to ipc:///tmp/joystick_control" << std::endl;

            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ZmqReceiver] Exception in connect(): " << e.what() << std::endl;
            return false;
        }
    }

    void disconnect() {
        connected_ = false;

        if (socket_) {
            zmq_close(socket_);
            socket_ = nullptr;
        }

        if (context_) {
            zmq_ctx_destroy(context_);
            context_ = nullptr;
        }
    }

    // 启动接收线程
    void start() {
        if (!connected_ || !data_callback_) {
            std::cerr << "[ZmqReceiver] Cannot start: not connected or no callback registered" << std::endl;
            return;
        }

        running_ = true;
        receiver_thread_ = std::thread(&SimpleZmqReceiver::receiverLoop, this);
        std::cout << "[ZmqReceiver] Started receiver thread" << std::endl;
    }

    // 停止接收线程
    void stop() {
        running_ = false;
        if (receiver_thread_.joinable()) {
            receiver_thread_.join();
            std::cout << "[ZmqReceiver] Stopped receiver thread" << std::endl;
        }
    }

private:
    // 接收线程主循环
    void receiverLoop() {
        std::cout << "[ZmqReceiver] Receiver thread started" << std::endl;

        while (running_ && connected_) {
            JoystickData data;
            if (receiveData(data) && data_callback_) {
                data_callback_(data);
            }

            // 短暂休眠避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::microseconds(1000));
        }

        std::cout << "[ZmqReceiver] Receiver thread finished" << std::endl;
    }

private:
    // RAII包装器用于自动管理ZMQ消息
    struct ZmqMessage {
        zmq_msg_t msg;
        ZmqMessage() { zmq_msg_init(&msg); }
        ~ZmqMessage() { zmq_msg_close(&msg); }
        zmq_msg_t* get() { return &msg; }
    };

    // 接收ZMQ消息的辅助函数
    bool receiveZmqMessage(ZmqMessage& msg, const char* error_context) {
        int rc = zmq_msg_recv(msg.get(), socket_, ZMQ_DONTWAIT);
        if (rc == -1) {
            if (errno != EAGAIN) {
                std::cerr << "[ZmqReceiver] Failed to receive " << error_context
                         << ": " << zmq_strerror(errno) << std::endl;
            }
            return false;
        }
        return true;
    }

    // 接收数据的内部方法
    bool receiveData(JoystickData& data) {
        if (!connected_) return false;

        try {
            // 接收主题部分
            ZmqMessage topic_msg;
            if (!receiveZmqMessage(topic_msg, "topic")) {
                return false;
            }

            // 检查是否有数据部分
            int more;
            size_t more_size = sizeof(more);
            zmq_getsockopt(socket_, ZMQ_RCVMORE, &more, &more_size);
            if (!more) {
                std::cerr << "[ZmqReceiver] Expected multipart message" << std::endl;
                return false;
            }

            // 接收数据部分
            ZmqMessage data_msg;
            if (!receiveZmqMessage(data_msg, "data")) {
                return false;
            }

            // 验证数据大小
            size_t data_size = zmq_msg_size(data_msg.get());
            if (data_size != sizeof(JoystickRawData)) {
                std::cerr << "[ZmqReceiver] Data size mismatch. Expected: " << sizeof(JoystickRawData)
                         << ", Got: " << data_size << std::endl;
                return false;
            }

            // 解析并转换数据
            const JoystickRawData* raw_data = static_cast<const JoystickRawData*>(zmq_msg_data(data_msg.get()));

            // 直接赋值位姿数据
            data.pose[0] = raw_data->x;   data.pose[1] = raw_data->y;   data.pose[2] = raw_data->z;
            data.pose[5] = raw_data->rx;  data.pose[4] = raw_data->ry;  data.pose[3] = raw_data->rz;

            // 赋值按钮状态
            data.start = raw_data->start;
            data.suck = raw_data->suck;
            data.reset = raw_data->reset;
            data.record = raw_data->record;
            data.valid = true;

            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ZmqReceiver] Exception in receiveData(): " << e.what() << std::endl;
            return false;
        }
    }
};

int main() {
    // // 注册信号处理器
    // signal(SIGINT, signalHandler);
    // signal(SIGTERM, signalHandler);

    std::cout << "=== ZMQ Joystick Control with industRob (Callback Version) ===" << std::endl;

    // 创建机器人控制实例
<<<<<<< HEAD
    industRob robot(CommType::ZMQ, "../config/cartesian_robot/urdf/cartesian_6axis.urdf", "tool0");
=======
    industRob robot(CommType::ZMQ);
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

    // 连接机器人
    std::cout << "Connecting to robot..." << std::endl;
    robot.connect();

    if (!robot.isConnected()) {
        std::cerr << "Failed to connect to robot" << std::endl;
        return 1;
    }

    std::cout << "Robot connected successfully" << std::endl;

    // // 初始化机器人
    // robot.init();

    // // 设置伺服模式参数
    // robot.servoMode(1, 10, 1.0, 0.1);  // mode=1, period=10ms, smooth=1.0, gain=0.1

    // 创建运动数据处理器
    MotionDataProcessor motion_processor(&robot);
    motion_processor.start();

    // 创建手柄数据接收器
    SimpleZmqReceiver receiver;

    // 状态变量
    int message_count = 0;
    auto last_print_time = std::chrono::steady_clock::now();
    int last_start = 0, last_suck = 0, last_reset = 0, last_record = 0;

    // 注册数据处理回调函数
    receiver.registerCallback([&](const JoystickData& data) {
        message_count++;
        auto current_time = std::chrono::steady_clock::now();

        auto time_since_last_print = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - last_print_time);

        // 检测按钮状态变化
        if (data.start == 1 && last_start == 0) {
            std::cout << ">>> START button pressed! Initializing robot..." << std::endl;
            try {
                std::cout << ">>> Robot initialized successfully" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << ">>> Error initializing robot: " << e.what() << std::endl;
            }
        }

        if (data.record == 2 && last_record != 2) {
            std::cout << ">>> RECORDING started..." << std::endl;
        } else if (last_record == 2 && data.record == 0) {
            std::cout << ">>> RECORDING stopped." << std::endl;
        }

        // 解析函数：根据 reset 标志执行不同的动作
        if (data.reset == 1) {
            // 1. 判断是否 data.reset == 1，如果满足，则执行 robReset 函数
            std::cout << ">>> RESET=1: Executing robot reset..." << std::endl;
            try {
                //    --------------------------------------------------------
                std::cout << ">>> Robot reset completed successfully" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << ">>> Error executing robot reset: " << e.what() << std::endl;
            }
        } else if (data.reset != 1 && data.valid) {
            // 2. 判断是否 data.reset != 1，执行将运动数据推入有锁队列
            std::vector<double> pose(data.pose, data.pose + 6);
            motion_processor.pushMotionData(pose);
        }

        // 更新上次状态
        last_start = data.start;
        last_suck = data.suck;
        last_reset = data.reset;
        last_record = data.record;
    });

    // 连接到发布者
    if (!receiver.connect()) {
        std::cerr << "Failed to connect to joystick publisher" << std::endl;
        motion_processor.stop();
        robot.disconnect();
        return 1;
    }

    // 启动接收器
    receiver.start();

    std::cout << "Waiting for joystick data..." << std::endl;
    std::cout << "Press Ctrl+C to exit" << std::endl;
    std::cout << std::string(80, '-') << std::endl;

    // 主循环等待退出信号
    while (running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    std::cout << "\nTotal messages received: " << message_count << std::endl;

    // 停止所有组件
    receiver.stop();
    motion_processor.stop();
    receiver.disconnect();
    robot.disconnect();

    std::cout << "All components stopped and disconnected successfully" << std::endl;

    return 0;
}
