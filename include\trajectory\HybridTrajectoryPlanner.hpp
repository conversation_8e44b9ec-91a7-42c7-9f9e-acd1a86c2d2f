#ifndef HYBRID_TRAJECTORY_PLANNER_HPP
#define HYBRID_TRAJECTORY_PLANNER_HPP

#include "TrajInterpolatorBase.hpp"
#include "ToppraInterpolator.hpp"
#include "RuckigInterpolator.hpp"
#include <memory>
#include <queue>
#include <mutex>
#include <atomic>
#include <vector>
#include <functional>

// 轨迹段状态枚举
enum class SegmentStatus {
    PENDING,     // 等待执行
    ACTIVE,      // 正在执行
    COMPLETED,   // 已完成
    CANCELLED    // 已取消
};

// 轨迹段定义
template<int DOF>
struct TrajectorySegment {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    
    std::vector<MotionState<DOF>> waypoints;  // 航向点
    std::vector<TrajectoryState<DOF>> trajectory_points;  // 插补后的轨迹点
    std::vector<double> timestamps;           // 时间戳
    double start_time = 0.0;                 // 段开始时间
    double duration = 0.0;                   // 段持续时间
    SegmentStatus status = SegmentStatus::PENDING;
    bool is_transition = false;              // 是否为过渡段
    size_t segment_id = 0;                   // 段ID
    
    TrajectorySegment() = default;
    TrajectorySegment(const std::vector<MotionState<DOF>>& wpts, size_t id = 0)
        : waypoints(wpts), segment_id(id) {}
};

// 轨迹切换事件
template<int DOF>
struct TrajectoryTransitionEvent {
    MotionState<DOF> current_state;          // 当前状态
    std::vector<MotionState<DOF>> new_waypoints;  // 新轨迹航向点
    double transition_duration = 0.5;        // 过渡时间
    bool smooth_transition = true;           // 是否平滑过渡
    size_t priority = 0;                     // 优先级
};

// 混合轨迹规划器
template<int DOF>
class HybridTrajectoryPlanner {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    using SegmentPtr = std::shared_ptr<TrajectorySegment<DOF>>;
    using TransitionCallback = std::function<void(const TrajectoryTransitionEvent<DOF>&)>;
    using CompletionCallback = std::function<void(size_t segment_id)>;

private:
    // 核心插补器
    std::unique_ptr<ToppraInterpolator<DOF>> offline_planner_;
    std::unique_ptr<RuckigInterpolator<DOF>> online_tracker_;
    
    // 轨迹段管理
    std::queue<SegmentPtr> segment_queue_;
    SegmentPtr current_segment_;
    SegmentPtr transition_segment_;
    mutable std::mutex segment_mutex_;
    
    // 状态管理
    std::atomic<bool> is_active_{false};
    std::atomic<bool> is_transitioning_{false};
    std::atomic<size_t> next_segment_id_{1};
    MotionState<DOF> current_state_;
    mutable std::mutex state_mutex_;
    
    // 配置参数
    double dt_ = 0.001;  // 时间步长
    MotionConstraints<DOF> constraints_;
    
    // 回调函数
    TransitionCallback transition_callback_;
    CompletionCallback completion_callback_;

public:
    explicit HybridTrajectoryPlanner(double dt = 0.001, const std::string& urdf_path = "")
        : dt_(dt) {
        offline_planner_ = std::make_unique<ToppraInterpolator<DOF>>(dt, urdf_path);
        online_tracker_ = std::make_unique<RuckigInterpolator<DOF>>(dt, urdf_path);
    }

    virtual ~HybridTrajectoryPlanner() = default;

    // 设置运动约束
    void setConstraints(const MotionConstraints<DOF>& constraints) {
        constraints_ = constraints;
        offline_planner_->setConstraints(constraints);
        online_tracker_->setConstraints(constraints);
    }

    // 设置回调函数
    void setTransitionCallback(const TransitionCallback& callback) {
        transition_callback_ = callback;
    }

    void setCompletionCallback(const CompletionCallback& callback) {
        completion_callback_ = callback;
    }

    // 添加轨迹段（离线规划）
    bool addTrajectorySegment(const std::vector<MotionState<DOF>>& waypoints, bool immediate = false);

    // 请求轨迹切换
    bool requestTrajectoryTransition(const std::vector<MotionState<DOF>>& new_waypoints, 
                                   double transition_duration = 0.5);

    // 获取当前目标状态（在线跟踪）
    TrajectoryState<DOF> getCurrentTarget(double current_time);

    // 更新当前状态
    void updateCurrentState(const MotionState<DOF>& state);

    // 启动/停止规划器
    bool start();
    void stop();
    bool isActive() const { return is_active_.load(); }

    // 状态查询
    bool isTransitioning() const { return is_transitioning_.load(); }
    size_t getQueueSize() const;
    SegmentPtr getCurrentSegment() const;

    // 清空队列
    void clearQueue();

    // 紧急停止
    bool emergencyStop();

private:
    // 内部方法
    bool planOfflineSegment(SegmentPtr segment);
    TrajectoryState<DOF> executeOnlineTracking(SegmentPtr segment, double current_time);
    bool generateTransitionSegment(const MotionState<DOF>& from_state,
                                 const MotionState<DOF>& to_state,
                                 double duration);
    
    void processSegmentQueue();
    void switchToNextSegment();
    bool isSegmentCompleted(SegmentPtr segment, double current_time) const;
    
    // 轨迹连续性检查
    bool checkTrajectoryContinuity(const TrajectorySegment<DOF>& seg1,
                                   const TrajectorySegment<DOF>& seg2) const;
    
    // 安全检查
    bool validateWaypoints(const std::vector<MotionState<DOF>>& waypoints) const;
    bool checkConstraints(const TrajectoryState<DOF>& state) const;
};

#endif // HYBRID_TRAJECTORY_PLANNER_HPP
