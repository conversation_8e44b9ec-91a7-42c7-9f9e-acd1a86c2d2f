#include "trajectory/UnifiedController.h"
#include "trajectory/TrajectoryTypes.h"
#include "industRob.h"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <cmath>
#include <fstream>
#include <iomanip>

using namespace trajectory;

class UnifiedTrajectoryTest {
private:
    std::unique_ptr<UnifiedController> controller_;
    std::unique_ptr<industRob> robot_;
    std::string urdf_path_;
    
    // 预定义的关键点 (6DOF)
    Eigen::VectorXd point_a_, point_b_, point_c_;  // 离线轨迹 A->B->C
    Eigen::VectorXd point_d_, point_e_, point_f_;  // 离线轨迹 D->E->F
    
    // 在线跟踪目标序列
    std::vector<Eigen::VectorXd> online_targets_;
    size_t online_target_index_;
    
    // 记录轨迹数据
    std::vector<TrajectoryState> recorded_trajectory_;
    std::ofstream log_file_;
    
    // 控制标志
    std::atomic<bool> is_running_;
    std::atomic<bool> record_trajectory_;
    
public:
    UnifiedTrajectoryTest(const std::string& urdf_path) 
        : urdf_path_(urdf_path), online_target_index_(0), is_running_(false), record_trajectory_(true) {
        
        // 初始化关键点
        initializeKeyPoints();
        
        // 生成在线跟踪目标序列
        generateOnlineTargets();
        
        // 打开日志文件
        log_file_.open("unified_trajectory_log.csv");
        log_file_ << "timestamp,mode,pos_1,pos_2,pos_3,pos_4,pos_5,pos_6,"
                  << "vel_1,vel_2,vel_3,vel_4,vel_5,vel_6\n";
    }
    
    ~UnifiedTrajectoryTest() {
        if (log_file_.is_open()) {
            log_file_.close();
        }
    }
    
    bool initialize() {
        // 初始化机器人连接
        robot_ = std::make_unique<industRob>();
        robot_->connect();
        
        // 创建控制参数配置
        ControlParameters params(6);  // 6DOF机器人
        params.setControlFrequency(1000.0);  // 1kHz

        // 设置运动约束
        params.max_velocity = Eigen::VectorXd::Constant(6, 1.2);   // rad/s
        params.max_acceleration = Eigen::VectorXd::Constant(6, 6.0);   // rad/s²
        params.max_jerk = Eigen::VectorXd::Constant(6, 25.0); // rad/s³

        // 设置其他参数
        params.transition_duration = 0.5;  // 模式切换过渡时间
        params.lookahead_time = 0.1;       // 前瞻时间
        params.servo_gain = 0.5;           // 伺服增益

        // 打印配置信息
        std::cout << "=== 控制参数配置 ===" << std::endl;
        params.print();

        // 创建统一控制器并传递参数
        controller_ = std::make_unique<UnifiedController>(urdf_path_, params);
        
        // 设置伺服命令回调 - 对接servoJ
        controller_->setServoCommandCallback([this](const TrajectoryState& target) {
            handleServoCommand(target);
        });
        
        // 设置事件回调
        controller_->setEventCallback([this](const std::string& event, const TrajectoryState& state) {
            handleEvent(event, state);
        });
        
        if (!controller_->initialize()) {
            std::cerr << "Failed to initialize controller" << std::endl;
            return false;
        }
        
        // 约束已在控制参数中设置，无需重复设置
        
        // 设置初始状态为点A
        TrajectoryState initial_state(6);
        initial_state.position = point_a_;
        initial_state.velocity = Eigen::VectorXd::Zero(6);
        initial_state.acceleration = Eigen::VectorXd::Zero(6);
        initial_state.timestamp = 0.0;
        initial_state.valid = true;
        controller_->setInitialState(initial_state);
        
        std::cout << "=== 统一轨迹控制测试初始化完成 ===" << std::endl;
        printKeyPoints();
        
        return true;
    }
    
    void runCompleteTest() {
        if (!controller_ || !robot_) {
            std::cerr << "Controller or robot not initialized" << std::endl;
            return;
        }
        
        // 启动控制器
        controller_->start();
        is_running_ = true;
        
        std::cout << "\n=== 开始完整轨迹测试 ===" << std::endl;
        
        // 阶段1: 离线运动 A->B->C
        runOfflinePhase_ABC();
        
        // 阶段2: 在线跟踪
        runOnlinePhase();
        
        // 阶段3: 离线运动 D->E->F
        runOfflinePhase_DEF();
        
        // 停止控制器
        is_running_ = false;
        controller_->stop();
        
        // 分析和保存结果
        analyzeTrajectory();
        
        std::cout << "\n=== 完整轨迹测试完成 ===" << std::endl;
    }
    
private:
    void initializeKeyPoints() {
        // 定义6DOF机器人的关键点 (单位: 弧度)
        point_a_ = Eigen::VectorXd::Zero(6);
        point_a_ << 0.0, -0.5, 0.0, -1.5, 0.0, 1.0;
        
        point_b_ = Eigen::VectorXd::Zero(6);
        point_b_ << 0.3, -0.3, 0.2, -1.2, 0.1, 0.8;
        
        point_c_ = Eigen::VectorXd::Zero(6);
        point_c_ << 0.6, 0.0, 0.4, -0.8, 0.2, 0.6;
        
        // D点将在在线跟踪结束后确定
        point_d_ = Eigen::VectorXd::Zero(6);
        
        point_e_ = Eigen::VectorXd::Zero(6);
        point_e_ << -0.2, 0.3, -0.1, -1.0, -0.1, 0.9;
        
        point_f_ = Eigen::VectorXd::Zero(6);
        point_f_ << -0.4, 0.6, -0.2, -0.6, -0.2, 1.2;
    }
    
    void generateOnlineTargets() {
        // 生成在线跟踪的目标序列（螺旋轨迹）
        const int num_targets = 15;
        const double radius_start = 0.1;
        const double radius_end = 0.25;
        const double height_change = 0.15;
        
        online_targets_.clear();
        for (int i = 0; i < num_targets; ++i) {
            double t = static_cast<double>(i) / (num_targets - 1);
            double angle = 3 * M_PI * t; // 1.5圈螺旋
            double radius = radius_start + (radius_end - radius_start) * t;
            
            Eigen::VectorXd target = point_c_; // 从C点开始
            target[0] += radius * std::cos(angle);
            target[1] += radius * std::sin(angle);
            target[2] += height_change * t;
            
            online_targets_.push_back(target);
        }
        
        std::cout << "生成了 " << online_targets_.size() << " 个在线目标点" << std::endl;
    }
    
    void runOfflinePhase_ABC() {
        std::cout << "\n--- 阶段1: 离线轨迹 A->B->C ---" << std::endl;
        
        // 创建路径点序列
        std::vector<Eigen::VectorXd> waypoints_abc = {point_a_, point_b_, point_c_};
        
        // 设置离线轨迹
        if (!controller_->setOfflineTrajectory(waypoints_abc, false)) {
            std::cerr << "Failed to set A->B->C trajectory" << std::endl;
            return;
        }
        
        // 切换到离线模式
        controller_->switchToOfflineMode();
        
        // 等待模式切换完成
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 监控轨迹执行
        monitorTrajectoryExecution("A->B->C", point_c_, 8.0);
    }
    
    void runOnlinePhase() {
        std::cout << "\n--- 阶段2: 在线跟踪 ---" << std::endl;
        
        // 切换到在线模式
        controller_->switchToOnlineMode();
        
        // 等待模式切换完成
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        online_target_index_ = 0;
        const double target_update_interval = 0.6; // 每600ms更新一次目标
        
        auto start_time = std::chrono::steady_clock::now();
        auto last_target_update = start_time;
        
        while (is_running_ && online_target_index_ < online_targets_.size()) {
            auto current_time = std::chrono::steady_clock::now();
            double elapsed_since_update = std::chrono::duration<double>(
                current_time - last_target_update).count();
            
            // 更新在线目标
            if (elapsed_since_update >= target_update_interval) {
                if (online_target_index_ < online_targets_.size()) {
                    Eigen::VectorXd target = online_targets_[online_target_index_];
                    controller_->setOnlineTarget(target);
                    
                    std::cout << "设置在线目标 " << (online_target_index_ + 1) 
                              << "/" << online_targets_.size() 
                              << ": [" << target.head(3).transpose() << "]" << std::endl;
                    
                    online_target_index_++;
                    last_target_update = current_time;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
        
        // 记录当前位置作为D点
        TrajectoryState current_state = controller_->getCurrentState();
        if (current_state.valid) {
            point_d_ = current_state.position;
            std::cout << "在线跟踪结束，当前位置D: " << point_d_.head(3).transpose() << std::endl;
        }
        
        std::cout << "在线跟踪阶段完成" << std::endl;
    }
    
    void runOfflinePhase_DEF() {
        std::cout << "\n--- 阶段3: 离线轨迹 D->E->F ---" << std::endl;
        
        // 创建路径点序列（从当前位置D开始）
        std::vector<Eigen::VectorXd> waypoints_def = {point_d_, point_e_, point_f_};
        
        // 设置离线轨迹
        if (!controller_->setOfflineTrajectory(waypoints_def, false)) {
            std::cerr << "Failed to set D->E->F trajectory" << std::endl;
            return;
        }
        
        // 切换到离线模式
        controller_->switchToOfflineMode();
        
        // 等待模式切换完成
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        // 监控轨迹执行
        monitorTrajectoryExecution("D->E->F", point_f_, 8.0);
    }
    
    void monitorTrajectoryExecution(const std::string& phase_name, 
                                   const Eigen::VectorXd& target_point, 
                                   double max_duration) {
        auto start_time = std::chrono::steady_clock::now();
        
        while (is_running_) {
            auto current_time = std::chrono::steady_clock::now();
            double elapsed = std::chrono::duration<double>(current_time - start_time).count();
            
            if (elapsed > max_duration) {
                std::cout << phase_name << " 轨迹执行完成 (时间: " << elapsed << "s)" << std::endl;
                break;
            }
            
            // 检查是否接近目标点
            TrajectoryState current_state = controller_->getCurrentState();
            if (current_state.valid) {
                double distance_to_target = (current_state.position - target_point).norm();
                if (distance_to_target < 0.02 && current_state.velocity.norm() < 0.05) {
                    std::cout << phase_name << " 到达目标，距离误差: " << distance_to_target << std::endl;
                    break;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    void handleServoCommand(const TrajectoryState& target) {
        if (!target.valid) {
            std::cerr << "Invalid trajectory state received" << std::endl;
            return;
        }
        
        // 核心：调用机器人的servoJ接口
        std::vector<double> joint_positions(6);
        for (int i = 0; i < 6; ++i) {
            joint_positions[i] = target.position[i];
        }
        
        double dt = 0.001;           // 1ms时间步长
        double lookahead_time = 0.1; // 前瞻时间
        double gain = 0.5;           // 增益
        
        robot_->servoj(joint_positions, dt, lookahead_time, gain);
        
        // 记录轨迹数据
        if (record_trajectory_) {
            recorded_trajectory_.push_back(target);
        }
        
        // 记录到日志文件
        if (log_file_.is_open()) {
            log_file_ << std::fixed << std::setprecision(6)
                      << target.timestamp << ","
                      << static_cast<int>(controller_->getCurrentMode()) << ",";
            
            for (int i = 0; i < 6; ++i) {
                log_file_ << target.position[i] << ",";
            }
            for (int i = 0; i < 6; ++i) {
                log_file_ << target.velocity[i];
                if (i < 5) log_file_ << ",";
            }
            log_file_ << "\n";
        }
        
        // 定期打印状态
        static size_t command_count = 0;
        if (++command_count % 200 == 0) {
            std::cout << "ServoJ #" << command_count 
                      << " | 模式: " << static_cast<int>(controller_->getCurrentMode())
                      << " | 位置: [" << target.position.head(3).transpose() << "]" << std::endl;
        }
    }
    
    void handleEvent(const std::string& event, const TrajectoryState& state) {
        std::cout << ">>> 事件: " << event << std::endl;
    }
    
    void printKeyPoints() {
        std::cout << "关键点定义:" << std::endl;
        std::cout << "  起点A: " << point_a_.head(3).transpose() << std::endl;
        std::cout << "  中间点B: " << point_b_.head(3).transpose() << std::endl;
        std::cout << "  终点C: " << point_c_.head(3).transpose() << std::endl;
        std::cout << "  中间点E: " << point_e_.head(3).transpose() << std::endl;
        std::cout << "  终点F: " << point_f_.head(3).transpose() << std::endl;
    }
    
    void analyzeTrajectory() {
        std::cout << "\n=== 轨迹分析 ===" << std::endl;
        std::cout << "记录的轨迹点数: " << recorded_trajectory_.size() << std::endl;
        
        if (!recorded_trajectory_.empty()) {
            double total_duration = recorded_trajectory_.back().timestamp - recorded_trajectory_.front().timestamp;
            std::cout << "总执行时间: " << total_duration << " 秒" << std::endl;
            std::cout << "平均频率: " << recorded_trajectory_.size() / total_duration << " Hz" << std::endl;
        }
        
        auto stats = controller_->getStatistics();
        std::cout << "控制循环统计:" << std::endl;
        std::cout << "  总循环数: " << stats.total_cycles << std::endl;
        std::cout << "  平均循环时间: " << stats.avg_cycle_time_ms << " ms" << std::endl;
        std::cout << "  最大循环时间: " << stats.max_cycle_time_ms << " ms" << std::endl;
        
        std::cout << "轨迹数据已保存到: unified_trajectory_log.csv" << std::endl;
    }
};

int main(int argc, char** argv) {
    std::string urdf_path = "path/to/robot.urdf";
    
    if (argc > 1) {
        urdf_path = argv[1];
    }
    
    std::cout << "=== 统一轨迹控制测试 ===" << std::endl;
    std::cout << "测试流程: A->B->C (离线) -> 螺旋跟踪 (在线) -> D->E->F (离线)" << std::endl;
    std::cout << "URDF路径: " << urdf_path << std::endl;
    
    try {
        UnifiedTrajectoryTest test(urdf_path);
        
        if (!test.initialize()) {
            std::cerr << "测试初始化失败" << std::endl;
            return -1;
        }
        
        // 运行完整测试
        test.runCompleteTest();
        
        std::cout << "\n测试成功完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
