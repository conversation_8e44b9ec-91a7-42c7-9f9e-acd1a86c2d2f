#include "HWComm.h"
#include "CommFactory.h"
#include <iostream>
#include <chrono>
#include <cstring>

HWComm::HWComm(CommType type)
    : current_type_(type)
    , stop_flag_(false)
    , gen_(rd_())
    , noise_dist_(-0.001, 0.001)
{
    // ZMQ 模式下 is_server=true，FIFO 模式下 is_server=true
    comm_ = CommFactory::createComm(type, true);
}

HWComm::~HWComm() {
    close();
}

bool HWComm::init() {
    if (!comm_) {
        std::cerr << "Failed to create communication instance" << std::endl;
        return false;
    }
    
    if (!comm_->init()) {
        std::cerr << "Failed to initialize communication" << std::endl;
        return false;
    }
    
    stop_flag_ = false;
    cmd_thread_ = std::thread(&HWComm::cmd_loop, this);
    state_thread_ = std::thread(&HWComm::state_loop, this);
    
    std::cout << "HWComm initialized with " 
              << (current_type_ == CommType::ZMQ ? "ZMQ" : "FIFO") 
              << " communication" << std::endl;
    return true;
}

void HWComm::close() {
    stop_flag_ = true;
    
    if (cmd_thread_.joinable()) {
        cmd_thread_.join();
    }
    if (state_thread_.joinable()) {
        state_thread_.join();
    }
    
    if (comm_) {
        comm_->close();
    }
}

void HWComm::run() {
    std::cout << "Hardware simulation started. Press Ctrl+C to stop." << std::endl;
    
    while (!stop_flag_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

std::string HWComm::requestReply(const std::string& cmd) {
    if (!comm_) return "";
    return comm_->requestReply(cmd);
}

bool HWComm::sendRtData(const RobotState& state) {
    if (!comm_) return false;
    
    // 打印状态信息
    std::cout << "HWComm: Sending robot state - "
              << "position: [" << state.position[0] << ", " 
              << state.position[1] << ", " << state.position[2] << ", "
              << state.position[3] << ", " << state.position[4] << ", "
              << state.position[5] << "]"
              << " velocity: [" << state.velocity[0] << ", " 
              << state.velocity[1] << ", " << state.velocity[2] << ", "
              << state.velocity[3] << ", " << state.velocity[4] << ", "
              << state.velocity[5] << "]"
              << " timestamp: " << state.timestamp_us << std::endl;
    
    return comm_->sendRtData(state);
}

bool HWComm::recvRtData(const std::function<void(const ServoCommand&)>& callback) {
    if (!comm_) return false;
    rt_data_callback_ = callback;
    
    // 设置接收回调，打印接收到的命令信息
    auto wrapped_callback = [this, callback](const ServoCommand& cmd) {
        std::cout << "HWComm: Received servo command - "
                  << "position: [" << cmd.position[0] << ", " 
                  << cmd.position[1] << ", " << cmd.position[2] << ", "
                  << cmd.position[3] << ", " << cmd.position[4] << ", "
                  << cmd.position[5] << "]"
                  << " velocity: [" << cmd.velocity[0] << ", " 
                  << cmd.velocity[1] << ", " << cmd.velocity[2] << ", "
                  << cmd.velocity[3] << ", " << cmd.velocity[4] << ", "
                  << cmd.velocity[5] << "]"
                  << " duration: " << cmd.duration_ms << "ms"
                  << " timestamp: " << cmd.timestamp_us << std::endl;
        
        // 调用原始回调
        if (callback) {
            callback(cmd);
        }
    };
    
    return comm_->recvRtData(wrapped_callback);
}

std::string HWComm::recvReply() {
    if (!comm_) return "";
    return comm_->recvReply();
}

bool HWComm::sendFeedback(const std::string& feedback) {
    if (!comm_) return false;
    return comm_->sendFeedbackData(feedback);
}

bool HWComm::recvFeedback(const std::function<void(const std::string&)>& callback) {
    if (!comm_) return false;
    return comm_->recvFeedbackData(callback);
}

void HWComm::switchCommType(CommType type) {
    if (type == current_type_) return;
    
    std::cout << "Switching communication type from " 
              << (current_type_ == CommType::ZMQ ? "ZMQ" : "FIFO")
              << " to " << (type == CommType::ZMQ ? "ZMQ" : "FIFO") << std::endl;
    
    close();
    current_type_ = type;
    comm_ = CommFactory::createComm(type, type == CommType::ZMQ);
    init();
}

std::string HWComm::get_connection_info() const {
    if (!comm_) return "No communication instance";
    return comm_->getConnectionInfo();
}

uint64_t HWComm::get_timestamp_us() const {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()
    ).count();
}

void HWComm::cmd_loop() {
    // 定义ServoCommand接收回调
    auto combined_callback = [this](const ServoCommand& cmd) {
        // 内置打印
<<<<<<< HEAD
        std::cout << "[HWComm] Recv: pos=[";
        for (int i = 0; i < 6; ++i) std::cout << cmd.position[i] << (i<5?", ":"");
        std::cout << "] vel=[";
        for (int i = 0; i < 6; ++i) std::cout << cmd.velocity[i] << (i<5?", ":"");
        std::cout << "] dt=" << cmd.duration_ms << " tp=" << cmd.timestamp_us << std::endl;
=======
        std::cout << "[HWComm] Received servo command: position=[";
        for (int i = 0; i < 6; ++i) std::cout << cmd.position[i] << (i<5?", ":"");
        std::cout << "] velocity=[";
        for (int i = 0; i < 6; ++i) std::cout << cmd.velocity[i] << (i<5?", ":"");
        std::cout << "] duration=" << cmd.duration_ms << " timestamp=" << cmd.timestamp_us << std::endl;
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

        // 调用用户注册的回调
        if (rt_data_callback_) {
            rt_data_callback_(cmd);
        }
    };

    while (!stop_flag_) {
        if (comm_) {
            // 处理请求-回复命令
            // 注意：ZMQComm::recvReply() 已经自动处理了回复发送
            std::string cmd_received = comm_->recvReply();
            if (!cmd_received.empty()) {
                std::cout << "[Hardware] Processed command, result: " << cmd_received << std::endl;
                // 不需要手动发送回复，ZMQComm已经处理了
            }

            // 持续检查ServoCommand消息
            comm_->recvRtData(combined_callback);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void HWComm::state_loop() {
    auto start_time = std::chrono::steady_clock::now();
    while (!stop_flag_) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time);
        if (elapsed.count() % 1 == 0) {
            simulate_hardware_state();
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

std::string HWComm::handle_command(const std::string& cmd) {
    std::cout << "[Hardware] Received command: " << cmd << std::endl;
    
    if (cmd == "SYS_INIT") {
        return "OK: System initialized";
    } else if (cmd == "GET_STATE") {
        return "OK: State=100";
    } else if (cmd == "RESET") {
        return "OK: System reset";
    } else if (cmd == "QUERY_ORIGIN") {
        return "OK: Origin valid";
    } else if (cmd == "HOMING") {
        return "OK: Homing completed";
    } else if (cmd == "START_SERVO") {
        return "OK: Servo started";
    } else if (cmd == "STOP_SERVO") {
        return "OK: Servo stopped";
    } else {
        return "ERROR: Unknown command";
    }
}

void HWComm::simulate_hardware_state() {
    RobotState cmd;
    cmd.timestamp_us = get_timestamp_us();
    
    // 模拟位置、速度数据
    for (int i = 0; i < 6; ++i) {
        double base_pos = 10.0 + i * 5.0 + std::sin(cmd.timestamp_us * 0.000001) * 2.0;
        double base_vel = 5.0 + i * 2.0 + std::cos(cmd.timestamp_us * 0.000001) * 1.0;
        double base_cur = 1.0 + i * 0.5 + std::sin(cmd.timestamp_us * 0.000001) * 0.5;

        cmd.position[i] = base_pos + noise_dist_(gen_);
        cmd.velocity[i] = base_vel + noise_dist_(gen_);
        cmd.current[i] = base_cur + noise_dist_(gen_);
        cmd.servo_status = 0;   
    }
    
    // 发送状态
    if (comm_) {
        comm_->sendRtData(cmd);
    }
} 