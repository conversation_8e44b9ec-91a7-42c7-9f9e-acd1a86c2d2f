#!/usr/bin/env python3
"""
继电器控制测试脚本
提供语义化的继电器控制接口和测试功能
"""

import zmq
import json
import time


class RelayController:
    """继电器控制器 - 提供语义化的控制接口"""
    
    def __init__(self, server_address="tcp://localhost:5555"):
        """
        初始化继电器控制器
        
        Args:
            server_address: ZeroMQ服务器地址
        """
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.REQ)
        self.socket.connect(server_address)
        
        # 控制映射配置
        self.control_mapping = {
            "suck": {  # 吸合控制
                "relay": 2,  # 继电器2控制夹爪吸合
                "on_state": 1,   # 通气时的状态
                "off_state": 0   # 断气时的状态
            },
            "rot": {   # 旋转/复位控制  
                "relay": 1,  # 继电器1控制平台复位
                "on_state": 0,   # 复位时的状态（不通气）
                "off_state": 1   # 非复位时的状态
            }
        }
    
    def _send_relay_command(self, relay, state):
        """
        发送继电器控制命令
        
        Args:
            relay: 继电器编号
            state: 继电器状态
            
        Returns:
            dict: 服务器响应
        """
        try:
            request = {"action": "control", "relay": relay, "state": state}
            self.socket.send_string(json.dumps(request))
            response = json.loads(self.socket.recv_string())
            return response
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def control(self, action, state):
        """
        语义化控制接口
        
        Args:
            action: 控制动作 ("suck" 或 "rot")
            state: 控制状态 (1=开启, 0=关闭)
            
        Returns:
            dict: 控制结果
        """
        if action not in self.control_mapping:
            return {"success": False, "error": f"未知的控制动作: {action}"}
        
        mapping = self.control_mapping[action]
        relay = mapping["relay"]
        
        # 根据状态选择对应的继电器状态
        if state == 1:
            relay_state = mapping["on_state"]
        else:
            relay_state = mapping["off_state"]
        
        print(f"执行控制: {action}={state} -> 继电器{relay}={relay_state}")
        response = self._send_relay_command(relay, relay_state)
        
        return {
            "success": response.get("success", False),
            "action": action,
            "state": state,
            "relay": relay,
            "relay_state": relay_state,
            "response": response
        }
    
    def suck(self, state):
        """
        夹爪吸合控制
        
        Args:
            state: 1=吸合(通气), 0=松开(断气)
        """
        return self.control("suck", state)
    
    def rot(self, state):
        """
        平台旋转/复位控制
        
        Args:
            state: 1=复位(不通气), 0=正常状态
        """
        return self.control("rot", state)
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
        if self.context:
            self.context.term()
    
    def __enter__(self):
        """支持with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句"""
        self.close()


def test_relay_basic():
    """原始继电器测试函数"""
    print("=== 基础继电器控制测试 ===")
    
    # 创建ZeroMQ上下文和socket
    context = zmq.Context()
    socket = context.socket(zmq.REQ)
    socket.connect("tcp://localhost:5555")
    
    try:
        # 测试继电器1
        print("开启继电器1...")
        request = {"action": "control", "relay": 1, "state": 1}
        socket.send_string(json.dumps(request))
        response = json.loads(socket.recv_string())
        print(f"响应: {response}")
        time.sleep(1)
        
        print("关闭继电器1...")
        request = {"action": "control", "relay": 1, "state": 0}
        socket.send_string(json.dumps(request))
        response = json.loads(socket.recv_string())
        print(f"响应: {response}")
        time.sleep(1)
        
        # 测试继电器2
        print("开启继电器2...")
        request = {"action": "control", "relay": 2, "state": 1}
        socket.send_string(json.dumps(request))
        response = json.loads(socket.recv_string())
        print(f"响应: {response}")
        time.sleep(1)
        
        print("关闭继电器2...")
        request = {"action": "control", "relay": 2, "state": 0}
        socket.send_string(json.dumps(request))
        response = json.loads(socket.recv_string())
        print(f"响应: {response}")
        
        print("基础测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        socket.close()
        context.term()


def test_relay_semantic():
    """语义化继电器控制测试"""
    print("=== 语义化继电器控制测试 ===")
    
    with RelayController() as controller:
        try:
            # 测试夹爪吸合控制
            print("\n1. 测试夹爪吸合控制:")
            print("夹爪吸合...")
            result = controller.suck(1)  # 吸合
            print(f"结果: {result}")
            time.sleep(2)
            
            print("夹爪松开...")
            result = controller.suck(0)  # 松开
            print(f"结果: {result}")
            time.sleep(2)
            
            # 测试平台复位控制
            print("\n2. 测试平台复位控制:")
            print("平台复位...")
            result = controller.rot(1)  # 复位
            print(f"结果: {result}")
            time.sleep(2)
            
            print("平台正常...")
            result = controller.rot(0)  # 正常状态
            print(f"结果: {result}")
            time.sleep(2)
            
            # 测试通用控制接口
            print("\n3. 测试通用控制接口:")
            print("使用control接口控制吸合...")
            result = controller.control("suck", 1)
            print(f"结果: {result}")
            time.sleep(1)
            
            print("使用control接口控制松开...")
            result = controller.control("suck", 0)
            print(f"结果: {result}")
            
            print("\n=== 语义化测试完成 ===")
            
        except Exception as e:
            print(f"测试失败: {e}")


def demo_usage():
    """演示使用方法"""
    print("=== 使用方法演示 ===")
    print("1. 基本用法:")
    print("   controller = RelayController()")
    print("   controller.suck(1)  # 夹爪吸合")
    print("   controller.suck(0)  # 夹爪松开")
    print("   controller.rot(1)   # 平台复位")
    print("   controller.rot(0)   # 平台正常")
    print()
    print("2. 使用with语句:")
    print("   with RelayController() as controller:")
    print("       controller.suck(1)")
    print()
    print("3. 通用控制接口:")
    print("   controller.control('suck', 1)  # 等同于 controller.suck(1)")
    print("   controller.control('rot', 1)   # 等同于 controller.rot(1)")
    print()
    print("4. 控制映射说明:")
    print("   suck=1 -> 继电器2=1 (通气夹爪吸合)")
    print("   suck=0 -> 继电器2=0 (断气夹爪松开)")
    print("   rot=1  -> 继电器1=0 (不通气平台复位)")
    print("   rot=0  -> 继电器1=1 (通气平台正常)")


def test_relay():
    """主测试函数 - 可选择测试模式"""
    print("继电器控制测试程序")
    print("选择测试模式:")
    print("1. 基础继电器测试")
    print("2. 语义化控制测试")
    print("3. 两种测试都运行")
    print("4. 显示使用方法")
    
    choice = input("请输入选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        test_relay_basic()
    elif choice == "2":
        test_relay_semantic()
    elif choice == "3":
        test_relay_basic()
        print("\n" + "="*50 + "\n")
        test_relay_semantic()
    elif choice == "4":
        demo_usage()
    else:
        print("默认运行语义化控制测试...")
        test_relay_semantic()


if __name__ == "__main__":
    test_relay()
