#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>
#include <Eigen/Dense>

#include "trajectory/ToppraInterpolatorBack.hpp"

using namespace Eigen;

int main() {
    std::cout << "=== TOPPRA 多种插值方法演示 ===" << std::endl;
    std::cout << "演示如何使用不同的插值方法来确保轨迹不会超出途经点" << std::endl;
    std::cout << std::string(60, '-') << std::endl;

    // 创建测试途经点
    std::vector<MotionState<6>> waypoints;
    
    // 途经点1: 起始点
    MotionState<6> wp1;
    wp1.position << 0.0, 0.0, 0.1, 0.0, 0.0, 0.0;
    wp1.velocity << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
    wp1.acceleration.setZero();
    waypoints.push_back(wp1);

    // 途经点2: 中间点1
    MotionState<6> wp2;
    wp2.position << 0.2, 0.1, 0.05, 0.0, 0.0, 0.0;
    wp2.velocity << 0.1, 0.0, 0.0, 0.0, 0.0, 0.0;
    wp2.acceleration.setZero();
    waypoints.push_back(wp2);

    // 途经点3: 中间点2
    MotionState<6> wp3;
    wp3.position << 0.3, 0.0, 0.03, 0.0, 0.0, 0.0;
    wp3.velocity << 0.0, -0.1, 0.0, 0.0, 0.0, 0.0;
    wp3.acceleration.setZero();
    waypoints.push_back(wp3);

    // 途经点4: 终点
    MotionState<6> wp4;
    wp4.position << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
    wp4.velocity << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
    wp4.acceleration.setZero();
    waypoints.push_back(wp4);

    std::cout << "测试途经点数量: " << waypoints.size() << std::endl;
    for (size_t i = 0; i < waypoints.size(); ++i) {
        std::cout << "  途经点" << i+1 << ": [" 
                  << waypoints[i].position.transpose() << "]" << std::endl;
    }
    std::cout << std::string(60, '-') << std::endl;

    // 设置运动约束
    MotionConstraints<6> constraints;
    constraints.max_velocity << 1.0, 1.0, 0.5, 1.0, 1.0, 1.0;
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);

    // 测试不同的插值方法
    std::vector<std::pair<PathInterpolationMethod, std::string>> methods = {
        {PathInterpolationMethod::CUBIC_SPLINE, "三次样条（默认，平滑但可能超出途经点）"},
        {PathInterpolationMethod::CUBIC_HERMITE_SPLINE, "三次Hermite样条（严格通过途经点，需要速度信息）"},
        {PathInterpolationMethod::LINEAR_SEGMENTS, "分段线性（严格通过途经点，但不平滑）"}
    };

    for (const auto& method_pair : methods) {
        std::cout << "\n=== 测试方法: " << method_pair.second << " ===" << std::endl;
        
        // 创建插值器
        ToppraInterpolator<6> interpolator(0.01);  // 10ms采样间隔
        interpolator.setConstraints(constraints);
        interpolator.setInterpolationMethod(method_pair.first);
        
        // 测试不同的边界条件
        std::vector<std::pair<BoundaryConditionType, std::string>> boundary_conditions = {
            {BoundaryConditionType::CLAMPED, "固定边界（首末速度为0）"},
            {BoundaryConditionType::NATURAL, "自然边界（首末加速度为0）"},
            {BoundaryConditionType::NOT_A_KNOT, "非节点边界（最平滑）"}
        };

        for (const auto& bc_pair : boundary_conditions) {
            if (method_pair.first == PathInterpolationMethod::CUBIC_HERMITE_SPLINE && 
                bc_pair.first != BoundaryConditionType::CLAMPED) {
                // Hermite样条不需要边界条件，跳过其他边界条件
                continue;
            }
            
            if (method_pair.first == PathInterpolationMethod::LINEAR_SEGMENTS && 
                bc_pair.first != BoundaryConditionType::CLAMPED) {
                // 线性插值不需要边界条件，跳过其他边界条件
                continue;
            }

            std::cout << "\n  边界条件: " << bc_pair.second << std::endl;
            interpolator.setBoundaryCondition(bc_pair.first);

            // 计算轨迹
            auto start_time = std::chrono::high_resolution_clock::now();
            bool success = interpolator.computeOffline(waypoints);
            auto end_time = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            
            if (success) {
                std::cout << "  ✓ 计算成功，耗时: " << duration.count() << " μs" << std::endl;
                std::cout << "  插值器类型: " << interpolator.getTypeName() << std::endl;
                
                // 验证轨迹是否通过途经点
                std::cout << "  验证途经点通过情况:" << std::endl;
                double total_duration = interpolator.getDuration();
                std::cout << "  轨迹总时长: " << std::fixed << std::setprecision(3) 
                          << total_duration << " 秒" << std::endl;
                
                // 检查起点和终点
                auto start_state = interpolator.getState(0.0);
                auto end_state = interpolator.getState(total_duration);
                
                if (start_state.valid && end_state.valid) {
                    double start_error = (start_state.position - waypoints[0].position).norm();
                    double end_error = (end_state.position - waypoints.back().position).norm();
                    
                    std::cout << "    起点误差: " << std::scientific << std::setprecision(2) 
                              << start_error << " m" << std::endl;
                    std::cout << "    终点误差: " << std::scientific << std::setprecision(2) 
                              << end_error << " m" << std::endl;
                    
                    if (start_error < 1e-6 && end_error < 1e-6) {
                        std::cout << "    ✓ 起点和终点精确通过" << std::endl;
                    } else {
                        std::cout << "    ⚠ 起点或终点存在误差" << std::endl;
                    }
                }
                
            } else {
                std::cout << "  ✗ 计算失败: " << interpolator.getLastError() << std::endl;
            }
        }
    }

    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "推荐使用方案:" << std::endl;
    std::cout << "1. 如果需要严格通过途经点且有速度信息: 使用 CUBIC_HERMITE_SPLINE" << std::endl;
    std::cout << "2. 如果需要严格通过途经点但轨迹可以不平滑: 使用 LINEAR_SEGMENTS" << std::endl;
    std::cout << "3. 如果需要平滑轨迹且可以容忍轻微偏离途经点: 使用 CUBIC_SPLINE" << std::endl;
    std::cout << std::string(60, '=') << std::endl;

    return 0;
}
