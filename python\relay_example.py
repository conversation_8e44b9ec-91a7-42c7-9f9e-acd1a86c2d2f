#!/usr/bin/env python3
"""
继电器控制使用示例
演示如何使用语义化的继电器控制接口
"""

from relay_test import RelayController
import time


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建控制器
    controller = RelayController()
    
    try:
        # 夹爪操作
        print("1. 夹爪吸合")
        result = controller.suck(1)
        print(f"   结果: {result['success']}")
        time.sleep(1)
        
        print("2. 夹爪松开")
        result = controller.suck(0)
        print(f"   结果: {result['success']}")
        time.sleep(1)
        
        # 平台操作
        print("3. 平台复位")
        result = controller.rot(1)
        print(f"   结果: {result['success']}")
        time.sleep(1)
        
        print("4. 平台正常")
        result = controller.rot(0)
        print(f"   结果: {result['success']}")
        
    finally:
        controller.close()


def example_with_statement():
    """使用with语句的示例"""
    print("\n=== 使用with语句示例 ===")
    
    with RelayController() as controller:
        # 执行一个完整的抓取动作序列
        print("执行抓取序列:")
        
        print("1. 平台复位")
        controller.rot(1)
        time.sleep(1)
        
        print("2. 夹爪吸合")
        controller.suck(1)
        time.sleep(2)
        
        print("3. 平台恢复")
        controller.rot(0)
        time.sleep(1)
        
        print("4. 夹爪松开")
        controller.suck(0)
        
        print("抓取序列完成!")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    with RelayController() as controller:
        # 测试无效的控制动作
        result = controller.control("invalid_action", 1)
        if not result['success']:
            print(f"预期的错误: {result['error']}")
        
        # 正常的控制
        result = controller.control("suck", 1)
        if result['success']:
            print("正常控制成功")
        else:
            print(f"控制失败: {result.get('error', '未知错误')}")


def example_custom_sequence():
    """自定义动作序列示例"""
    print("\n=== 自定义动作序列示例 ===")
    
    def pick_and_place_sequence(controller):
        """拾取和放置动作序列"""
        print("开始拾取和放置序列...")
        
        # 步骤1: 准备阶段
        print("  步骤1: 平台复位")
        controller.rot(1)
        time.sleep(0.5)
        
        # 步骤2: 抓取
        print("  步骤2: 夹爪吸合")
        controller.suck(1)
        time.sleep(1)
        
        # 步骤3: 移动（这里只是模拟）
        print("  步骤3: 移动到目标位置（模拟）")
        time.sleep(1)
        
        # 步骤4: 放置
        print("  步骤4: 夹爪松开")
        controller.suck(0)
        time.sleep(0.5)
        
        # 步骤5: 恢复
        print("  步骤5: 平台恢复")
        controller.rot(0)
        
        print("拾取和放置序列完成!")
    
    with RelayController() as controller:
        pick_and_place_sequence(controller)


def main():
    """主函数"""
    print("继电器控制使用示例")
    print("=" * 40)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_with_statement()
        example_error_handling()
        example_custom_sequence()
        
        print("\n" + "=" * 40)
        print("所有示例运行完成!")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        print("请确保继电器服务器正在运行 (tcp://localhost:5555)")


if __name__ == "__main__":
    main()
