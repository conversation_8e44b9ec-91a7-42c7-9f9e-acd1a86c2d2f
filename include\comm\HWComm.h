#ifndef HW_COMM_H
#define HW_COMM_H

#include <atomic>
#include "CommBase.h"
#include <memory>
#include <thread>
#include <random>
#include <functional>

// 功能指令枚举
enum class RobotCommand {
    SYS_INIT,
    GET_STATE,
    RESET,
    QUERY_ORIGIN,
    HOMING,
    START_SERVO,
    STOP_SERVO,
    UNKNOWN
};

class HWComm {
public:
    explicit HWComm(CommType type = CommType::ZMQ);
    ~HWComm();
    
    // 基础连接管理
    bool init();
    void close();
    void run();
    
    // Request-Reply 模式：机器人通信层向硬件层请求执行功能指令
    std::string requestReply(const std::string& cmd);
    
    // 实时数据发送：机器人通信层发送给硬件板卡层，没有返回
    bool sendRtData(const RobotState& cmd);
    
    // 实时数据接收：硬件板卡层发送给机器人通信层，回调函数形式
    bool recvRtData(const std::function<void(const ServoCommand&)>& callback);
    
    // 中断接收指令，返回结果：硬件板卡接收功能指令，根据枚举执行，返回结果
    std::string recvReply();
    
    // 反馈通道：硬件到机器人
    bool sendFeedback(const std::string& feedback);
    bool recvFeedback(const std::function<void(const std::string&)>& callback);
    
    // 工具方法
    void switchCommType(CommType type);
    std::string get_connection_info() const;
    uint64_t get_timestamp_us() const;

private:
    CommType current_type_;
    std::unique_ptr<CommBase> comm_;
    std::thread cmd_thread_;
    std::thread state_thread_;
    std::atomic<bool> stop_flag_;
    
    // 随机数生成器
    std::random_device rd_;
    std::mt19937 gen_;
    std::normal_distribution<double> noise_dist_;
    
    // 回调函数
    std::function<void(const ServoCommand&)> rt_data_callback_;
    
    // 内部方法
    void cmd_loop();
    void state_loop();
    std::string handle_command(const std::string& cmd);
    void simulate_hardware_state();
};

#endif // HW_COMM_H 