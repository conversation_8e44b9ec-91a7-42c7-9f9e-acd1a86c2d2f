#include "industRob.h"
#include <cstring>
#include <memory>
#include <iostream>
#include <thread>
#include <cmath>
<<<<<<< HEAD
#include <algorithm>
#include <atomic>
=======
<<<<<<< HEAD
#include <algorithm>
=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
#include "utils.hpp"

class industRob::impl {
public:
    impl(CommType type)
<<<<<<< HEAD
        : rob_comm_(type)
        , robot_model_(nullptr) {
        initializeBuffers();
    }
=======
<<<<<<< HEAD
        : rob_comm_(type)
        , robot_model_(nullptr) {}
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

    impl(CommType type, const std::string& urdf_path, const std::string& end_effector_frame)
        : rob_comm_(type)
        , robot_model_(nullptr) {
<<<<<<< HEAD
        initializeBuffers();
        loadRobotModel(urdf_path, end_effector_frame);
    }
=======
        loadRobotModel(urdf_path, end_effector_frame);
    }
=======
        : rob_comm_(type) {}
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

    ~impl() = default;

    void connect() {
        rob_comm_.init();
        rob_comm_.recvRtData([this](const RobotState& state) {
            this->onRobotState(state);
        });
<<<<<<< HEAD

=======
<<<<<<< HEAD

=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    }

    void disconnect() {
        rob_comm_.close();
    }

    bool isConnected() {
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
        return rob_comm_.isConnected();
    }

    void init() {
        //rob_comm_.init_robot();
    }

    void init(const std::string& urdf_path, const std::string& end_effector_frame) {
        // 初始化通信
        //rob_comm_.init_robot();

        // 加载机器人模型
        if (!robot_model_) {
            loadRobotModel(urdf_path, end_effector_frame);
        }
<<<<<<< HEAD
=======
=======
        return rob_comm_.get_connection_info() != "No communication instance";
    }

    void init() {
        rob_comm_.init_robot();
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    }

    void reset() {
        rob_comm_.reset();
<<<<<<< HEAD
        // 重置机器人类型缓存
        robot_type_cached_ = false;
        is_cartesian_robot_ = false;
    }

=======
    }

<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
private:
    // ============================================================================
    // 机器人模型配置 (内部使用)
    // ============================================================================

    bool loadRobotModel(const std::string& urdf_path, const std::string& end_effector_frame) {
        try {
            robot_model_ = std::make_unique<robot_infra::RobotModel>(urdf_path, end_effector_frame);
            if (!robot_model_->initialize()) {
                std::cerr << "Failed to initialize robot model from: " << urdf_path << std::endl;
                robot_model_.reset();
<<<<<<< HEAD
                robot_type_cached_ = false;  // 重置缓存
                return false;
            }

            // 优化：在初始化时缓存机器人类型，避免实时回调中的字符串比较
            std::string robot_name = robot_model_->getRobotName();
            is_cartesian_robot_ = (robot_name.find("cartesian") != std::string::npos);
            robot_type_cached_ = true;

            std::cout << "Robot model loaded successfully. Type: "
                      << (is_cartesian_robot_ ? "Cartesian" : "Articulated")
                      << " (Name: " << robot_name << ")" << std::endl;
=======
                return false;
            }

            std::cout << "Robot model loaded successfully" << std::endl;
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            return true;

        } catch (const std::exception& e) {
            std::cerr << "Error loading robot model: " << e.what() << std::endl;
            robot_model_.reset();
<<<<<<< HEAD
            robot_type_cached_ = false;  // 重置缓存
=======
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            return false;
        }
    }

public:

<<<<<<< HEAD
=======
=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    void setMotionPara(double vec, double acc) {
        // 参数设置示例
        std::cout << "Set motion para: vec=" << vec << ", acc=" << acc << std::endl;
    }

    void servoMode(int mode, int controlPeriod, double smoothRatio, double responseRatio) {
        std::cout << "Set servo mode: mode=" << mode << ", period=" << controlPeriod
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
                  << ", smooth=" << smoothRatio << ", gain=" << responseRatio << std::endl;
        rob_comm_.start_servo(mode, responseRatio, smoothRatio, controlPeriod);
    }

    // ============================================================================
    // 运动学计算 (使用RobotModel)
    // ============================================================================

    std::vector<double> forwardKinematics(const std::vector<double>& jointRad) {
        if (!robot_model_) {
            std::cerr << "Robot model not loaded. Using fallback FK." << std::endl;
            // 简单模拟正解作为后备
            std::vector<double> pose(6, 0.0);
            for (size_t i = 0; i < 6 && i < jointRad.size(); ++i) {
                pose[i] = jointRad[i] * 0.5; // 假设线性关系
            }
            return pose;
        }

        try {
            Eigen::VectorXd q = Eigen::Map<const Eigen::VectorXd>(jointRad.data(), jointRad.size());
            
            robot_infra::RobotModel::Vector6d pose_eigen = robot_model_->forwardKinematics(q);

            std::vector<double> pose(pose_eigen.data(), pose_eigen.data() + pose_eigen.size());

            return pose;

        } catch (const std::exception& e) {
            std::cerr << "Error in forward kinematics: " << e.what() << std::endl;
            return std::vector<double>(6, 0.0);
        }
    }

    bool inverseKinematics(std::vector<double>& jointRad, const std::vector<double>& pose) {
        if (!robot_model_) {
            std::cerr << "Robot model not loaded. Using fallback IK." << std::endl;
            // 简单模拟逆解作为后备
            if (pose.size() < 6) return false;
            jointRad.resize(6);
            for (size_t i = 0; i < 6; ++i) {
                jointRad[i] = pose[i] / 0.5;
            }
            return true;
        }

        if (pose.size() != 6) {
            std::cerr << "Pose must have 6 elements [x, y, z, rx, ry, rz]" << std::endl;
            return false;
        }

        try {
            robot_infra::RobotModel::Vector6d target_pose = Eigen::Map<const Eigen::VectorXd>(pose.data(), 6);

            // 使用当前关节角度作为初始值
            Eigen::VectorXd q_init;
            if (jointRad.size() == robot_model_->getJointCount()) {
                q_init = Eigen::Map<const Eigen::VectorXd>(jointRad.data(), jointRad.size());
            } else {
                q_init = Eigen::VectorXd::Zero(robot_model_->getJointCount());
            }

            Eigen::VectorXd q_solution(robot_model_->getJointCount());
            bool ik_success = robot_model_->inverseKinematics(q_solution, target_pose, q_init);

            if (!ik_success) {
                std::cerr << "Warning: IK failed to converge" << std::endl;
                // 继续使用最后的解
<<<<<<< HEAD
                
=======
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            }

            // 使用RobotModel进行限位检查和处理
            if (!robot_model_->validateAndClampJointCommand(q_solution, "inverseKinematics")) {
                std::cerr << "Warning: IK solution violates joint limits and cannot be corrected" << std::endl;
                // 继续使用夹紧后的解
<<<<<<< HEAD
                return false;
=======
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            }

            jointRad.resize(q_solution.size());
            for (int i = 0; i < q_solution.size(); ++i) {
                jointRad[i] = q_solution[i];
            }

            return true;

        } catch (const std::exception& e) {
            std::cerr << "Error in inverse kinematics: " << e.what() << std::endl;
            return false;
        }
    }



private:
    // ============================================================================
    // 辅助方法 - 类型转换
    // ============================================================================

    Eigen::VectorXd vectorToEigen(const std::vector<double>& vec) const {
        return Eigen::Map<const Eigen::VectorXd>(vec.data(), vec.size());
    }

    std::vector<double> eigenToVector(const Eigen::VectorXd& eigen_vec) const {
        std::vector<double> result(eigen_vec.size());
        for (int i = 0; i < eigen_vec.size(); ++i) {
            result[i] = eigen_vec[i];
        }
        return result;
    }

    robot_infra::RobotModel::Vector6d vectorToPose(const std::vector<double>& vec) const {
        return Eigen::Map<const Eigen::VectorXd>(vec.data(), 6);
    }

    std::vector<double> poseToVector(const robot_infra::RobotModel::Vector6d& pose) const {
        std::vector<double> result(6);
        for (int i = 0; i < 6; ++i) {
            result[i] = pose[i];
        }
        return result;
    }

public:
    // ============================================================================
    // 运动指令 (带统一限位检查)
    // ============================================================================

    void movAbsJ(const std::vector<double>& jointRad, double acc, double vec, double blendRadius) {
        if (robot_model_) {
            // 使用RobotModel进行限位检查和处理
            Eigen::VectorXd joints_eigen = vectorToEigen(jointRad);
            if (!robot_model_->validateAndClampJointCommand(joints_eigen, "movAbsJ")) {
                std::cerr << "movAbsJ: Command rejected due to limit violations" << std::endl;
                return;
            }
            std::vector<double> safe_joints = eigenToVector(joints_eigen);

            std::cout << "movAbsJ: [";
            for (auto v : safe_joints) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        } else {
            std::cout << "movAbsJ: [";
            for (auto v : jointRad) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        }

        // 这里应该发送实际的运动指令到机器人
        // rob_comm_.sendMotionCommand(...);
    }

    void movJ(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius) {
        if (robot_model_) {
            // 使用RobotModel进行限位检查和处理
            robot_infra::RobotModel::Vector6d pose_eigen = vectorToPose(xyzabc);
            if (!robot_model_->validateAndClampCartesianCommand(pose_eigen, "movJ")) {
                std::cerr << "movJ: Command rejected due to limit violations" << std::endl;
                return;
            }
            std::vector<double> safe_pose = poseToVector(pose_eigen);

            // 计算逆运动学
            std::vector<double> jointRad;
            if (!inverseKinematics(jointRad, safe_pose)) {
                std::cerr << "movJ: Inverse kinematics failed" << std::endl;
                return;
            }

            // 检查关节限位
            Eigen::VectorXd joints_eigen = vectorToEigen(jointRad);
            if (!robot_model_->validateAndClampJointCommand(joints_eigen, "movJ")) {
                std::cerr << "movJ: Joint solution violates limits" << std::endl;
                return;
            }
            jointRad = eigenToVector(joints_eigen);

            std::cout << "movJ: [";
            for (auto v : safe_pose) std::cout << v << " ";
            std::cout << "] -> joints: [";
            for (auto v : jointRad) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        } else {
            std::cout << "movJ: [";
            for (auto v : xyzabc) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        }

        // 这里应该发送实际的运动指令到机器人
        // rob_comm_.sendMotionCommand(...);
    }

    void movL(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius) {
        if (robot_model_) {
            // 使用RobotModel进行限位检查和处理
            robot_infra::RobotModel::Vector6d pose_eigen = vectorToPose(xyzabc);
            if (!robot_model_->validateAndClampCartesianCommand(pose_eigen, "movL")) {
                std::cerr << "movL: Command rejected due to limit violations" << std::endl;
                return;
            }
            std::vector<double> safe_pose = poseToVector(pose_eigen);


            std::cout << "movL: [";
            for (auto v : safe_pose) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        } else {
            std::cout << "movL: [";
            for (auto v : xyzabc) std::cout << v << " ";
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        }

        // 这里应该发送实际的运动指令到机器人
        // rob_comm_.sendMotionCommand(...);
    }

    void movL(const std::vector<std::vector<double>>& path, double acc, double vec, double blendRadius) {
        if (path.empty()) {
            std::cerr << "movL: Empty path provided" << std::endl;
            return;
        }

        if (robot_model_) {
            // 检查路径中的每个点
            std::vector<std::vector<double>> safe_path;
            safe_path.reserve(path.size());

            for (size_t i = 0; i < path.size(); ++i) {
                robot_infra::RobotModel::Vector6d pose_eigen = vectorToPose(path[i]);
                if (!robot_model_->validateAndClampCartesianCommand(pose_eigen, "movL_path_point_" + std::to_string(i))) {
                    std::cerr << "movL: Path point " << i << " violates limits and cannot be corrected" << std::endl;
                    return;
                }
                std::vector<double> safe_pose = poseToVector(pose_eigen);

                // 检查逆运动学解
                std::vector<double> jointRad;
                if (!inverseKinematics(jointRad, safe_pose)) {
                    std::cerr << "movL: Inverse kinematics failed for path point " << i << std::endl;
                    return;
                }

                Eigen::VectorXd joints_eigen = vectorToEigen(jointRad);
                if (!robot_model_->validateAndClampJointCommand(joints_eigen, "movL_path_point_" + std::to_string(i))) {
                    std::cerr << "movL: Joint solution for path point " << i << " violates limits" << std::endl;
                    return;
                }

                safe_path.push_back(safe_pose);
            }

            std::cout << "movL path: [";
            for (const auto& pt : safe_path) {
                std::cout << "[";
                for (auto v : pt) std::cout << v << " ";
                std::cout << "] ";
            }
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        } else {
            std::cout << "movL path: [";
            for (const auto& pt : path) {
                std::cout << "[";
                for (auto v : pt) std::cout << v << " ";
                std::cout << "] ";
            }
            std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
        }

        // 这里应该发送实际的路径运动指令到机器人
        // rob_comm_.sendPathCommand(...);
<<<<<<< HEAD
=======
=======
                  << ", smoothRatio=" << smoothRatio << ", responseRatio=" << responseRatio << std::endl;
    }

    std::vector<double> forwardKinematics(const std::vector<double>& jointRad) {
        // 简单模拟正解
        std::vector<double> pose(6, 0.0);
        for (size_t i = 0; i < 6 && i < jointRad.size(); ++i) {
            pose[i] = jointRad[i] * 10.0; // 假设线性关系
        }
        return pose;
    }

    bool inverseKinematics(std::vector<double>& jointRad, const std::vector<double>& pose) {
        // 简单模拟逆解
        if (pose.size() < 6) return false;
        jointRad.resize(6);
        for (size_t i = 0; i < 6; ++i) {
            jointRad[i] = pose[i] / 10.0;
        }
        return true;
    }

    void movAbsJ(const std::vector<double>& jointRad, double acc, double vec, double blendRadius) {
        std::cout << "movAbsJ: [";
        for (auto v : jointRad) std::cout << v << " ";
        std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
    }

    void movJ(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius) {
        std::cout << "movJ: [";
        for (auto v : xyzabc) std::cout << v << " ";
        std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
    }

    void movL(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius) {
        std::cout << "movL: [";
        for (auto v : xyzabc) std::cout << v << " ";
        std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
    }

    void movL(const std::vector<std::vector<double>>& path, double acc, double vec, double blendRadius) {
        std::cout << "movL path: [";
        for (const auto& pt : path) {
            std::cout << "[";
            for (auto v : pt) std::cout << v << " ";
            std::cout << "] ";
        }
        std::cout << "] acc=" << acc << " vec=" << vec << " blend=" << blendRadius << std::endl;
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    }

    void waitRobFree() {
        std::cout << "waitRobFree..." << std::endl;
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
        //std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // ============================================================================
    // 伺服控制 (带统一限位检查)
    // ============================================================================

    void servoj(const std::vector<double>& jointRad, double dt, double lookahead_time, double gain) {
        std::vector<double> safe_joints = jointRad;

        if (robot_model_) {
            // 使用RobotModel进行限位检查和处理
            Eigen::VectorXd joints_eigen = vectorToEigen(jointRad);
            if (!robot_model_->validateAndClampJointCommand(joints_eigen, "servoj")) {
                std::cerr << "servoj: Command rejected due to limit violations" << std::endl;
                return;
            }
            safe_joints = eigenToVector(joints_eigen);
        }

        ServoCommand cmd;
        cmd.timestamp_us = get_timestamp_us();
        for (size_t i = 0; i < 6 && i < safe_joints.size(); ++i) {
            cmd.position[i] = safe_joints[i] * 1000.0;
<<<<<<< HEAD
=======
=======
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    void servoj(const std::vector<double>& jointRad, double dt, double lookahead_time, double gain) {
        
        ServoCommand cmd;
        cmd.timestamp_us = get_timestamp_us();
        for (size_t i = 0; i < 6 && i < jointRad.size(); ++i) {
            cmd.position[i] = jointRad[i];
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            cmd.velocity[i] = 0.0;
        }
        cmd.duration_ms = static_cast<uint32_t>(dt * 1000);
        rob_comm_.sendRtData(cmd);
<<<<<<< HEAD

        std::cout << "servoj: [";
        for (auto v : safe_joints) std::cout << v << " ";
=======
<<<<<<< HEAD

        std::cout << "servoj: [";
        for (auto v : safe_joints) std::cout << v << " ";
=======
        std::cout << "sendRtData finished, timestamp: " << cmd.duration_ms << std::endl;
        std::cout << "servoj: [";
        for (auto v : jointRad) std::cout << v << " ";
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
        std::cout << "] dt=" << dt << " lookahead=" << lookahead_time << " gain=" << gain << std::endl;
    }

    void servoL(const std::vector<double>& xyzabc, double dt, double lookahead_time, double gain) {
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
        std::vector<double> safe_pose = xyzabc;
        std::vector<double> jointRad;

        if (robot_model_) {
<<<<<<< HEAD
            // // 使用RobotModel进行限位检查和处理
            robot_infra::RobotModel::Vector6d pose_eigen = vectorToPose(xyzabc);

            if (!robot_model_->validateAndClampCartesianCommand(pose_eigen, "servoL")) {
                std::cerr << "servoL: Command rejected due to limit violations" << std::endl;
                //return;
            }

=======
            // 使用RobotModel进行限位检查和处理
            robot_infra::RobotModel::Vector6d pose_eigen = vectorToPose(xyzabc);
            if (!robot_model_->validateAndClampCartesianCommand(pose_eigen, "servoL")) {
                std::cerr << "servoL: Command rejected due to limit violations" << std::endl;
                return;
            }
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
            safe_pose = poseToVector(pose_eigen);
        }

        ServoCommand cmd;
        cmd.timestamp_us = get_timestamp_us();
        for (size_t i = 0; i < 6 && i < safe_pose.size(); ++i) {
            cmd.position[i] = safe_pose[i] * 1000.0;
            cmd.velocity[i] = 0.0;
        }
        cmd.duration_ms = static_cast<uint32_t>(dt * 1000);
        rob_comm_.sendRtData(cmd);

        std::cout << "servoL: [";
        for (auto v : safe_pose) std::cout << v << " ";
<<<<<<< HEAD
        
=======
        if (robot_model_) {
            std::cout << "] -> joints: [";
            for (auto v : jointRad) std::cout << v << " ";
        }
=======
        std::cout << "servoL: [";
        for (auto v : xyzabc) std::cout << v << " ";
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
        std::cout << "] dt=" << dt << " lookahead=" << lookahead_time << " gain=" << gain << std::endl;
    }

    void speedj(const std::vector<double>& jointRad, double acc, double dt) {
        std::cout << "speedj: [";
        for (auto v : jointRad) std::cout << v << " ";
        std::cout << "] acc=" << acc << " dt=" << dt << std::endl;
    }

    void speedL(const std::vector<double>& xyzabc, double acc, double dt) {
        std::cout << "speedL: [";
        for (auto v : xyzabc) std::cout << v << " ";
        std::cout << "] acc=" << acc << " dt=" << dt << std::endl;
    }

    void setIO(int index, int val) {
        std::cout << "setIO: index=" << index << " val=" << val << std::endl;
    }

    int getIO(int index) {
        std::cout << "getIO: index=" << index << std::endl;
        return 0;
    }

    std::vector<double> getJoint() {
<<<<<<< HEAD
        RobotFullState latest_state;
        if (getLatestState(latest_state)) {
            return latest_state.joint_position;
        }
        // 回退：返回默认值
        return std::vector<double>(6, 0.0);
    }

    std::vector<double> getTcpPose() {
        RobotFullState latest_state;
        if (getLatestState(latest_state)) {
            return latest_state.cartesian_position;
        }
        // 回退：返回默认值
        return std::vector<double>(6, 0.0);
    }

    std::vector<double> getJointSpeed() {
        RobotFullState latest_state;
        if (getLatestState(latest_state)) {
            return latest_state.joint_velocity;
        }
        // 回退：返回默认值
        return std::vector<double>(6, 0.0);
    }

    std::vector<double> getTcpSpeed() {
        RobotFullState latest_state;
        if (getLatestState(latest_state)) {
            return latest_state.cartesian_velocity;
        }
        // 回退：返回默认值
        return std::vector<double>(6, 0.0);
    }

=======
        std::vector<double> joints(6, 0.0);
        for (int i = 0; i < 6; ++i) joints[i] = std::sin(i);
        return joints;
    }

    std::vector<double> getTcpPose() {
        std::vector<double> pose(6, 0.0);
        for (int i = 0; i < 6; ++i) pose[i] = std::cos(i);
        return pose;
    }

    std::vector<double> getJointSpeed() {
        std::vector<double> speed(6, 0.0);
        for (int i = 0; i < 6; ++i) speed[i] = 0.1 * i;
        return speed;
    }

    std::vector<double> getTcpSpeed() {
        std::vector<double> speed(6, 0.0);
        for (int i = 0; i < 6; ++i) speed[i] = 0.2 * i;
        return speed;
    }

<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    std::string getConnectionInfo() const {
        std::string info = "industRob Connection Info:\n";
        info += "  Connected: " + std::string(rob_comm_.isConnected() ? "Yes" : "No") + "\n";
        info += "  Robot Model Loaded: " + std::string((robot_model_ != nullptr) ? "Yes" : "No") + "\n";
        if (robot_model_) {
            info += "  Joint Count: " + std::to_string(robot_model_->getJointCount()) + "\n";
            info += "  Limit Checking: Enabled (via RobotModel)\n";
        } else {
            info += "  Limit Checking: Disabled (no model)\n";
        }
        return info;
    }

<<<<<<< HEAD
=======
=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    double initPerio() {
        std::cout << "initPerio..." << std::endl;
        return 0.01;
    }

    void waitPeriod(double startTime, double TimeCorrection) {
        std::cout << "waitPeriod: start=" << startTime << " corr=" << TimeCorrection << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    void registerStateCallback(const std::function<void(const RobotFullState&)>& cb) {
        state_cb_ = cb;
    }

    void onRobotState(const RobotState& state) {
<<<<<<< HEAD
        // 优化：使用预分配的缓存对象，避免重复构造和内存分配
        RobotFullState& full = cached_full_state_;
        full.timestamp_us = state.timestamp_us;
        full.servo_status = state.servo_status;

        // 优化：直接resize到固定大小并使用memcpy进行快速拷贝
        full.joint_position.resize(6);
        full.joint_velocity.resize(6);
        std::memcpy(full.joint_position.data(), state.position, 6 * sizeof(double));
        std::memcpy(full.joint_velocity.data(), state.velocity, 6 * sizeof(double));

        // 优化：使用初始化时缓存的机器人类型，避免实时回调中的判断
        if (robot_model_ && robot_type_cached_ && !is_cartesian_robot_) {
            // 优化：直接使用forwardKinematics，避免重复计算
            full.cartesian_position = forwardKinematics(full.joint_position);

            // 优化：使用预分配的临时变量，避免重复分配
            temp_joint_pos_ = Eigen::Map<const Eigen::VectorXd>(
                full.joint_position.data(), full.joint_position.size());
            temp_joint_vel_ = Eigen::Map<const Eigen::VectorXd>(
                full.joint_velocity.data(), full.joint_velocity.size());

            // 优化：直接计算到临时变量，然后拷贝到结果
            temp_cart_vel_ = robot_model_->computeJacobian(temp_joint_pos_) * temp_joint_vel_;

            // 优化：预分配大小并直接拷贝
            full.cartesian_velocity.resize(6);
            std::copy(temp_cart_vel_.data(), temp_cart_vel_.data() + 6,
                     full.cartesian_velocity.begin());
        } else {
            // 优化：对于笛卡尔机器人，直接赋值，避免拷贝
            full.cartesian_position = full.joint_position;
            full.cartesian_velocity = full.joint_velocity;
        }

        if (state_cb_) {
            state_cb_(full);
        }
        // 多线程优化：更新读取缓冲区（无锁双缓冲）
        updateReadBuffer(full);
    }

    // 初始化所有缓冲区
    void initializeBuffers() {
        // 预分配RobotFullState的vector大小，避免运行时分配
        cached_full_state_.joint_position.reserve(6);
        cached_full_state_.joint_velocity.reserve(6);
        cached_full_state_.cartesian_position.reserve(6);
        cached_full_state_.cartesian_velocity.reserve(6);

        // 初始化双缓冲区
        for (int i = 0; i < 2; ++i) {
            read_buffer_[i].joint_position.reserve(6);
            read_buffer_[i].joint_velocity.reserve(6);
            read_buffer_[i].cartesian_position.reserve(6);
            read_buffer_[i].cartesian_velocity.reserve(6);
        }
    }

    // 无锁双缓冲更新（实时回调中调用）
    void updateReadBuffer(const RobotFullState& state) {
        // 获取当前写缓冲区索引
        int write_idx = current_write_buffer_.load(std::memory_order_relaxed);

        // 快速拷贝到写缓冲区
        RobotFullState& write_buf = read_buffer_[write_idx];
        write_buf = state;  // 使用预分配的缓冲区，避免内存分配

        // 原子切换缓冲区（读者将读取另一个缓冲区）
        current_write_buffer_.store(1 - write_idx, std::memory_order_release);
        data_ready_.store(true, std::memory_order_release);
    }

    // 线程安全的状态读取（供get方法调用）
    bool getLatestState(RobotFullState& state) const {
        if (!data_ready_.load(std::memory_order_acquire)) {
            return false;
        }

        // 读取当前非写入缓冲区
        int write_idx = current_write_buffer_.load(std::memory_order_acquire);
        int read_idx = 1 - write_idx;

        // 快速拷贝最新状态
        state = read_buffer_[read_idx];
        return true;
=======
        RobotFullState full;
        full.timestamp_us = state.timestamp_us;
        full.joint_position.assign(state.position, state.position + 6);
        full.joint_velocity.assign(state.velocity, state.velocity + 6);
        full.servo_status = state.servo_status;
        full.cartesian_position = forwardKinematics(full.joint_position);
        full.cartesian_velocity = std::vector<double>(6, 0.0);
        if (state_cb_) {
            state_cb_(full);
        }
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    }

private:
    RobComm rob_comm_;
    std::function<void(const RobotFullState&)> state_cb_;
<<<<<<< HEAD

    // 机器人模型相关
    std::unique_ptr<robot_infra::RobotModel> robot_model_;

    // 实时回调优化缓存
    mutable bool robot_type_cached_ = false;
    mutable bool is_cartesian_robot_ = false;
    mutable Eigen::VectorXd temp_joint_pos_{6};
    mutable Eigen::VectorXd temp_joint_vel_{6};
    mutable Eigen::VectorXd temp_cart_vel_{6};

    // 预分配的RobotFullState，避免重复构造
    mutable RobotFullState cached_full_state_;

    // 多线程安全的状态读取优化 - 双缓冲区
    mutable RobotFullState read_buffer_[2];
    mutable std::atomic<int> current_write_buffer_{0};
    mutable std::atomic<bool> data_ready_{false};
=======
<<<<<<< HEAD

    // 机器人模型相关
    std::unique_ptr<robot_infra::RobotModel> robot_model_;
=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
};

// industRob 外部接口转发
industRob::industRob(CommType type)
    : pimpl_(std::make_unique<impl>(type)) {}

<<<<<<< HEAD
industRob::industRob(CommType type, const std::string& urdf_path, const std::string& end_effector_frame)
    : pimpl_(std::make_unique<impl>(type, urdf_path, end_effector_frame)) {}

=======
<<<<<<< HEAD
industRob::industRob(CommType type, const std::string& urdf_path, const std::string& end_effector_frame)
    : pimpl_(std::make_unique<impl>(type, urdf_path, end_effector_frame)) {}

=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
industRob::~industRob() = default;

void industRob::connect() { pimpl_->connect(); }
void industRob::disconnect() { pimpl_->disconnect(); }
bool industRob::isConnected() { return pimpl_->isConnected(); }
void industRob::init() { pimpl_->init(); }
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
void industRob::init(const std::string& urdf_path, const std::string& end_effector_frame) {
    pimpl_->init(urdf_path, end_effector_frame);
}
void industRob::reset() { pimpl_->reset(); }

void industRob::setMotionPara(double v, double a) { pimpl_->setMotionPara(v, a); }
void industRob::servoMode(int m, int p, double s, double r) { pimpl_->servoMode(m, p, s, r); }

// 运动学
<<<<<<< HEAD
=======
=======
void industRob::reset() { pimpl_->reset(); }
void industRob::setMotionPara(double v, double a) { pimpl_->setMotionPara(v, a); }
void industRob::servoMode(int m, int p, double s, double r) { pimpl_->servoMode(m, p, s, r); }
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
std::vector<double> industRob::forwardKinematics(const std::vector<double>& j) { return pimpl_->forwardKinematics(j); }
bool industRob::inverseKinematics(std::vector<double>& j, const std::vector<double>& p) { return pimpl_->inverseKinematics(j, p); }
void industRob::movAbsJ(const std::vector<double>& j, double a, double v, double b) { pimpl_->movAbsJ(j, a, v, b); }
void industRob::movJ(const std::vector<double>& x, double a, double v, double b) { pimpl_->movJ(x, a, v, b); }
void industRob::movL(const std::vector<double>& x, double a, double v, double b) { pimpl_->movL(x, a, v, b); }
void industRob::movL(const std::vector<std::vector<double>>& path, double acc, double vec, double blendRadius) { pimpl_->movL(path, acc, vec, blendRadius); }
void industRob::waitRobFree() { pimpl_->waitRobFree(); }
void industRob::servoj(const std::vector<double>& j, double dt, double l, double g) { pimpl_->servoj(j, dt, l, g); }
void industRob::servoL(const std::vector<double>& x, double dt, double l, double g) { pimpl_->servoL(x, dt, l, g); }
void industRob::speedj(const std::vector<double>& j, double a, double dt) { pimpl_->speedj(j, a, dt); }
void industRob::speedL(const std::vector<double>& x, double a, double dt) { pimpl_->speedL(x, a, dt); }
<<<<<<< HEAD

=======
<<<<<<< HEAD

=======
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
void industRob::setIO(int i, int v) { pimpl_->setIO(i, v); }
int industRob::getIO(int i) { return pimpl_->getIO(i); }
std::vector<double> industRob::getJoint() { return pimpl_->getJoint(); }
std::vector<double> industRob::getTcpPose() { return pimpl_->getTcpPose(); }
std::vector<double> industRob::getJointSpeed() { return pimpl_->getJointSpeed(); }
std::vector<double> industRob::getTcpSpeed() { return pimpl_->getTcpSpeed(); }
<<<<<<< HEAD
=======
<<<<<<< HEAD
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

double industRob::initPerio() { return pimpl_->initPerio(); }
void industRob::waitPeriod(double s, double c) { pimpl_->waitPeriod(s, c); }
void industRob::registerStateCallback(const std::function<void(const RobotFullState&)>& cb) { pimpl_->registerStateCallback(cb); }
<<<<<<< HEAD
std::string industRob::getConnectionInfo() const { return pimpl_->getConnectionInfo(); }
=======
std::string industRob::getConnectionInfo() const { return pimpl_->getConnectionInfo(); }
=======
double industRob::initPerio() { return pimpl_->initPerio(); }
void industRob::waitPeriod(double s, double c) { pimpl_->waitPeriod(s, c); }
void industRob::registerStateCallback(const std::function<void(const RobotFullState&)>& cb) { pimpl_->registerStateCallback(cb); } 
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
