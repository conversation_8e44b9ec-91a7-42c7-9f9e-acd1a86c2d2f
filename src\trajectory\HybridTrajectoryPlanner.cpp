#include "trajectory/HybridTrajectoryPlanner.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::addTrajectorySegment(
    const std::vector<MotionState<DOF>>& waypoints, bool immediate) {
    
    if (!validateWaypoints(waypoints)) {
        std::cerr << "Invalid waypoints provided" << std::endl;
        return false;
    }

    // 创建新轨迹段
    auto segment = std::make_shared<TrajectorySegment<DOF>>(waypoints, next_segment_id_++);
    
    // 离线规划轨迹
    if (!planOfflineSegment(segment)) {
        std::cerr << "Failed to plan offline segment" << std::endl;
        return false;
    }

    std::lock_guard<std::mutex> lock(segment_mutex_);
    
    if (immediate) {
        // 立即切换到新轨迹
        if (current_segment_) {
            current_segment_->status = SegmentStatus::CANCELLED;
        }

        // 清空队列并设置为当前段
        while (!segment_queue_.empty()) {
            segment_queue_.pop();
        }
        current_segment_ = segment;
        current_segment_->status = SegmentStatus::ACTIVE;
        current_segment_->start_time = -1.0;  // 标记为未开始，将在第一次调用时设置
    } else {
        // 添加到队列
        segment_queue_.push(segment);

        // 如果没有当前段，立即开始执行
        if (!current_segment_) {
            switchToNextSegment();
        }
    }

    std::cout << "Added trajectory segment " << segment->segment_id 
              << " with " << waypoints.size() << " waypoints" << std::endl;
    return true;
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::requestTrajectoryTransition(
    const std::vector<MotionState<DOF>>& new_waypoints, double transition_duration) {
    
    if (!validateWaypoints(new_waypoints)) {
        return false;
    }

    std::lock_guard<std::mutex> state_lock(state_mutex_);
    
    // 创建过渡事件
    TrajectoryTransitionEvent<DOF> event;
    event.current_state = current_state_;
    event.new_waypoints = new_waypoints;
    event.transition_duration = transition_duration;
    event.smooth_transition = true;

    // 生成过渡轨迹
    if (!generateTransitionSegment(current_state_, new_waypoints[0], transition_duration)) {
        std::cerr << "Failed to generate transition segment" << std::endl;
        return false;
    }

    // 规划新轨迹段
    auto new_segment = std::make_shared<TrajectorySegment<DOF>>(new_waypoints, next_segment_id_++);
    if (!planOfflineSegment(new_segment)) {
        std::cerr << "Failed to plan new trajectory segment" << std::endl;
        return false;
    }

    {
        std::lock_guard<std::mutex> segment_lock(segment_mutex_);
        
        // 取消当前段和队列中的段
        if (current_segment_) {
            current_segment_->status = SegmentStatus::CANCELLED;
        }
        
        while (!segment_queue_.empty()) {
            segment_queue_.pop();
        }

        // 设置过渡状态
        is_transitioning_.store(true);
        
        // 添加新段到队列
        segment_queue_.push(new_segment);
    }

    // 触发回调
    if (transition_callback_) {
        transition_callback_(event);
    }

    // std::cout << "Requested trajectory transition to " << new_waypoints.size() 
    //           << " waypoints with " << transition_duration << "s transition" << std::endl;
    return true;
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryPlanner<DOF>::getCurrentTarget(double current_time) {
    std::lock_guard<std::mutex> lock(segment_mutex_);
    
    // 处理过渡段
    if (is_transitioning_.load() && transition_segment_) {
        TrajectoryState<DOF> target = executeOnlineTracking(transition_segment_, current_time);
        if (isSegmentCompleted(transition_segment_, current_time)) {
            is_transitioning_.store(false);
            transition_segment_.reset();
            switchToNextSegment();
        }
        return target;
    }

    // 处理当前段
    if (current_segment_ && current_segment_->status == SegmentStatus::ACTIVE) {
        // 如果段刚开始，设置开始时间
        if (current_segment_->start_time < 0) {
            current_segment_->start_time = current_time;
            std::cout << "Starting segment " << current_segment_->segment_id
                      << " at time " << current_time << std::endl;
        }

        TrajectoryState<DOF> target = executeOnlineTracking(current_segment_, current_time);

        if (isSegmentCompleted(current_segment_, current_time)) {
            current_segment_->status = SegmentStatus::COMPLETED;

            // 触发完成回调
            if (completion_callback_) {
                completion_callback_(current_segment_->segment_id);
            }

            switchToNextSegment();
        }

        return target;
    }

    // 没有活动段，返回当前状态
    std::lock_guard<std::mutex> state_lock(state_mutex_);
    TrajectoryState<DOF> state;
    state.position = current_state_.position;
    state.velocity = current_state_.velocity;
    state.acceleration = current_state_.acceleration;
    state.timestamp = current_time;
    state.valid = true;
    return state;
}

template<int DOF>
void HybridTrajectoryPlanner<DOF>::updateCurrentState(const MotionState<DOF>& state) {
    std::lock_guard<std::mutex> lock(state_mutex_);
    current_state_ = state;
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::start() {
    if (is_active_.load()) {
        return true;
    }

    is_active_.store(true);
    std::cout << "HybridTrajectoryPlanner started" << std::endl;
    return true;
}

template<int DOF>
void HybridTrajectoryPlanner<DOF>::stop() {
    is_active_.store(false);
    is_transitioning_.store(false);
    
    std::lock_guard<std::mutex> lock(segment_mutex_);
    if (current_segment_) {
        current_segment_->status = SegmentStatus::CANCELLED;
    }
    
    while (!segment_queue_.empty()) {
        segment_queue_.pop();
    }
    
    std::cout << "HybridTrajectoryPlanner stopped" << std::endl;
}

template<int DOF>
size_t HybridTrajectoryPlanner<DOF>::getQueueSize() const {
    std::lock_guard<std::mutex> lock(segment_mutex_);
    return segment_queue_.size();
}

template<int DOF>
typename HybridTrajectoryPlanner<DOF>::SegmentPtr 
HybridTrajectoryPlanner<DOF>::getCurrentSegment() const {
    std::lock_guard<std::mutex> lock(segment_mutex_);
    return current_segment_;
}

template<int DOF>
void HybridTrajectoryPlanner<DOF>::clearQueue() {
    std::lock_guard<std::mutex> lock(segment_mutex_);
    while (!segment_queue_.empty()) {
        segment_queue_.pop();
    }
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::emergencyStop() {
    std::lock_guard<std::mutex> state_lock(state_mutex_);
    
    // 创建紧急停止目标状态
    MotionState<DOF> stop_state;
    stop_state.position = current_state_.position;
    stop_state.velocity = VectorDOF::Zero();
    stop_state.acceleration = VectorDOF::Zero();

    // 使用Ruckig生成紧急停止轨迹
    bool success = online_tracker_->computeOnline(current_state_, stop_state);
    
    if (success) {
        // 取消所有段
        std::lock_guard<std::mutex> segment_lock(segment_mutex_);
        if (current_segment_) {
            current_segment_->status = SegmentStatus::CANCELLED;
        }
        while (!segment_queue_.empty()) {
            segment_queue_.pop();
        }
        
        std::cout << "Emergency stop executed" << std::endl;
    }
    
    return success;
}

// 私有方法实现
template<int DOF>
bool HybridTrajectoryPlanner<DOF>::planOfflineSegment(SegmentPtr segment) {
    if (!segment || segment->waypoints.empty()) {
        return false;
    }

    try {
        // 使用TOPPRA进行离线规划
        bool success = offline_planner_->computeOffline(segment->waypoints);
        if (!success) {
            std::cerr << "TOPPRA offline planning failed: "
                      << offline_planner_->getLastError() << std::endl;
            return false;
        }

        // 获取轨迹缓冲区
        const auto& buffer = offline_planner_->getTrajectoryBuffer();
        segment->duration = buffer.getDuration();

        // 采样轨迹点
        double sample_time = 0.0;
        while (sample_time <= segment->duration) {
            auto state = buffer.getStateAtTime(sample_time);
            if (state.valid) {
                segment->trajectory_points.push_back(state);
                segment->timestamps.push_back(sample_time);
            }
            sample_time += dt_;
        }

        std::cout << "Planned offline segment " << segment->segment_id
                  << " with " << segment->trajectory_points.size()
                  << " points, duration: " << segment->duration << "s" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error in offline planning: " << e.what() << std::endl;
        return false;
    }
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryPlanner<DOF>::executeOnlineTracking(
    SegmentPtr segment, double current_time) {

    if (!segment || segment->trajectory_points.empty()) {
        TrajectoryState<DOF> invalid_state;
        invalid_state.valid = false;
        return invalid_state;
    }

    // 计算段内相对时间
    double relative_time = current_time - segment->start_time;

    // 在轨迹点中查找当前目标
    auto it = std::lower_bound(segment->timestamps.begin(), segment->timestamps.end(), relative_time);

    if (it == segment->timestamps.end()) {
        // 超出轨迹范围，返回最后一个点
        auto last_state = segment->trajectory_points.back();
        last_state.timestamp = current_time;
        return last_state;
    }

    size_t index = std::distance(segment->timestamps.begin(), it);
    if (index == 0) {
        auto first_state = segment->trajectory_points[0];
        first_state.timestamp = current_time;
        return first_state;
    }

    // 线性插值
    size_t prev_index = index - 1;
    double t1 = segment->timestamps[prev_index];
    double t2 = segment->timestamps[index];
    double alpha = (relative_time - t1) / (t2 - t1);

    TrajectoryState<DOF> interpolated_state;
    interpolated_state.position = (1.0 - alpha) * segment->trajectory_points[prev_index].position +
                                 alpha * segment->trajectory_points[index].position;
    interpolated_state.velocity = (1.0 - alpha) * segment->trajectory_points[prev_index].velocity +
                                 alpha * segment->trajectory_points[index].velocity;
    interpolated_state.acceleration = (1.0 - alpha) * segment->trajectory_points[prev_index].acceleration +
                                     alpha * segment->trajectory_points[index].acceleration;
    interpolated_state.timestamp = current_time;
    interpolated_state.valid = true;

    return interpolated_state;
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::generateTransitionSegment(
    const MotionState<DOF>& from_state, const MotionState<DOF>& to_state, double duration) {

    try {
        // 使用Ruckig生成过渡轨迹
        bool success = online_tracker_->computeOnline(from_state, to_state);
        if (!success) {
            std::cerr << "Failed to generate transition trajectory: "
                      << online_tracker_->getLastError() << std::endl;
            return false;
        }

        // 创建过渡段
        transition_segment_ = std::make_shared<TrajectorySegment<DOF>>();
        transition_segment_->is_transition = true;
        transition_segment_->segment_id = 0; // 特殊ID表示过渡段
        transition_segment_->start_time = 0.0; // 将在使用时设置
        transition_segment_->duration = std::min(duration, online_tracker_->getDuration());

        // 采样过渡轨迹
        const auto& buffer = online_tracker_->getTrajectoryBuffer();
        double sample_time = 0.0;
        while (sample_time <= transition_segment_->duration) {
            auto state = buffer.getStateAtTime(sample_time);
            if (state.valid) {
                transition_segment_->trajectory_points.push_back(state);
                transition_segment_->timestamps.push_back(sample_time);
            }
            sample_time += dt_;
        }

        // std::cout << "Generated transition segment with "
        //           << transition_segment_->trajectory_points.size()
        //           << " points, duration: " << transition_segment_->duration << "s" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error generating transition segment: " << e.what() << std::endl;
        return false;
    }
}

template<int DOF>
void HybridTrajectoryPlanner<DOF>::switchToNextSegment() {
    if (!segment_queue_.empty()) {
        current_segment_ = segment_queue_.front();
        segment_queue_.pop();
        current_segment_->status = SegmentStatus::ACTIVE;

        // 设置段的开始时间为未开始状态
        current_segment_->start_time = -1.0;

        std::cout << "Switched to segment " << current_segment_->segment_id << std::endl;
    } else {
        current_segment_.reset();
        std::cout << "No more segments in queue" << std::endl;
    }
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::isSegmentCompleted(SegmentPtr segment, double current_time) const {
    if (!segment || segment->timestamps.empty()) return true;

    // 计算段内相对时间
    double relative_time = current_time - segment->start_time;

    // 检查是否超过了轨迹的持续时间
    return relative_time >= segment->duration;
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::validateWaypoints(
    const std::vector<MotionState<DOF>>& waypoints) const {

    if (waypoints.empty()) {
        std::cerr << "Empty waypoints" << std::endl;
        return false;
    }

    for (const auto& wp : waypoints) {
        if (!checkConstraints(TrajectoryState<DOF>(wp.position, wp.velocity, wp.acceleration))) {
            std::cerr << "Waypoint violates constraints" << std::endl;
            return false;
        }
    }

    return true;
}

template<int DOF>
bool HybridTrajectoryPlanner<DOF>::checkConstraints(const TrajectoryState<DOF>& state) const {
    for (int i = 0; i < DOF; ++i) {
        if (std::abs(state.velocity[i]) > constraints_.max_velocity[i] ||
            std::abs(state.acceleration[i]) > constraints_.max_acceleration[i]) {
            return false;
        }
    }
    return true;
}

// 显式实例化常用的DOF
template class HybridTrajectoryPlanner<6>;
template class HybridTrajectoryPlanner<7>;
