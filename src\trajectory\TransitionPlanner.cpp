#include "trajectory/TransitionPlanner.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>
#include <numeric>

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state,
    double desired_duration) {
    
    TransitionResult<DOF> result;
    
    // 验证输入
    if (!validateTransition(from_state, to_state)) {
        result.error_message = "Invalid transition states";
        return result;
    }

    // 根据配置选择规划方法
    switch (config_.type) {
        case TransitionType::LINEAR:
            if (desired_duration > 0) {
                result = planLinearTransition(from_state, to_state, desired_duration);
            } else {
                result.error_message = "Linear transition requires specified duration";
            }
            break;
            
        case TransitionType::POLYNOMIAL:
            if (desired_duration > 0) {
                result = planPolynomialTransition(from_state, to_state, desired_duration);
            } else {
                result.error_message = "Polynomial transition requires specified duration";
            }
            break;
            
        case TransitionType::SPLINE:
            if (desired_duration > 0) {
                result = planSplineTransition(from_state, to_state, desired_duration);
            } else {
                result.error_message = "Spline transition requires specified duration";
            }
            break;
            
        case TransitionType::OPTIMAL_TIME:
            result = planOptimalTimeTransition(from_state, to_state);
            break;
            
        case TransitionType::SMOOTH_JERK:
            if (desired_duration > 0) {
                result = planSmoothJerkTransition(from_state, to_state, desired_duration);
            } else {
                double estimated_duration = estimateOptimalDuration(from_state, to_state);
                result = planSmoothJerkTransition(from_state, to_state, estimated_duration);
            }
            break;
            
        default:
            result.error_message = "Unknown transition type";
            return result;
    }

    // 评估轨迹质量
    if (result.success) {
        evaluateTrajectoryQuality(result);
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planOptimalTimeTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state) {
    
    TransitionResult<DOF> result;
    
    try {
        // 使用Ruckig进行时间最优规划
        bool success = ruckig_planner_->computeOnline(from_state, to_state);
        
        if (!success) {
            result.error_message = "Ruckig planning failed: " + ruckig_planner_->getLastError();
            return result;
        }

        // 获取轨迹
        const auto& buffer = ruckig_planner_->getTrajectoryBuffer();
        result.duration = buffer.getDuration();
        
        // 采样轨迹
        double sample_time = 0.0;
        while (sample_time <= result.duration) {
            auto state = buffer.getStateAtTime(sample_time);
            if (state.valid) {
                result.trajectory_points.push_back(state);
                result.timestamps.push_back(sample_time);
            }
            sample_time += dt_;
        }

        result.success = true;
        
        std::cout << "Planned optimal time transition: duration = " 
                  << result.duration << "s, points = " 
                  << result.trajectory_points.size() << std::endl;

    } catch (const std::exception& e) {
        result.error_message = "Exception in optimal time planning: " + std::string(e.what());
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planLinearTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state,
    double duration) {
    
    TransitionResult<DOF> result;
    
    if (duration <= 0) {
        result.error_message = "Invalid duration for linear transition";
        return result;
    }

    try {
        // 计算线性插值参数
        VectorDOF pos_diff = to_state.position - from_state.position;
        VectorDOF vel_diff = to_state.velocity - from_state.velocity;
        
        // 生成轨迹点
        int num_points = static_cast<int>(duration / dt_) + 1;
        result.trajectory_points.reserve(num_points);
        result.timestamps.reserve(num_points);

        for (int i = 0; i < num_points; ++i) {
            double t = std::min(i * dt_, duration);
            double alpha = t / duration;
            
            TrajectoryState<DOF> state;
            state.position = from_state.position + alpha * pos_diff;
            state.velocity = from_state.velocity + alpha * vel_diff;
            state.acceleration = VectorDOF::Zero(); // 线性过渡假设零加速度
            state.timestamp = t;
            state.valid = true;
            
            result.trajectory_points.push_back(state);
            result.timestamps.push_back(t);
        }

        result.duration = duration;
        result.success = true;
        
        std::cout << "Planned linear transition: duration = " 
                  << duration << "s, points = " 
                  << result.trajectory_points.size() << std::endl;

    } catch (const std::exception& e) {
        result.error_message = "Exception in linear planning: " + std::string(e.what());
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planPolynomialTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state,
    double duration) {
    
    TransitionResult<DOF> result;
    
    if (duration <= 0) {
        result.error_message = "Invalid duration for polynomial transition";
        return result;
    }

    try {
        // 使用5次多项式确保位置、速度、加速度连续性
        int num_points = static_cast<int>(duration / dt_) + 1;
        result.trajectory_points.reserve(num_points);
        result.timestamps.reserve(num_points);

        for (int dof = 0; dof < DOF; ++dof) {
            // 计算5次多项式系数
            double p0 = from_state.position[dof];
            double v0 = from_state.velocity[dof];
            double a0 = from_state.acceleration[dof];
            double p1 = to_state.position[dof];
            double v1 = to_state.velocity[dof];
            double a1 = to_state.acceleration[dof];
            
            double T = duration;
            double T2 = T * T;
            double T3 = T2 * T;
            double T4 = T3 * T;
            double T5 = T4 * T;
            
            // 5次多项式系数计算
            double c0 = p0;
            double c1 = v0;
            double c2 = a0 / 2.0;
            double c3 = (20*p1 - 20*p0 - (8*v1 + 12*v0)*T - (3*a0 - a1)*T2) / (2*T3);
            double c4 = (30*p0 - 30*p1 + (14*v1 + 16*v0)*T + (3*a0 - 2*a1)*T2) / (2*T4);
            double c5 = (12*p1 - 12*p0 - (6*v1 + 6*v0)*T - (a0 - a1)*T2) / (2*T5);
            
            // 生成轨迹点
            for (int i = 0; i < num_points; ++i) {
                double t = std::min(i * dt_, duration);
                double t2 = t * t;
                double t3 = t2 * t;
                double t4 = t3 * t;
                double t5 = t4 * t;
                
                if (i == 0) {
                    // 初始化轨迹点
                    for (int j = 0; j < num_points; ++j) {
                        result.trajectory_points.emplace_back();
                        result.timestamps.push_back(j * dt_);
                    }
                }
                
                // 计算位置、速度、加速度
                result.trajectory_points[i].position[dof] = c0 + c1*t + c2*t2 + c3*t3 + c4*t4 + c5*t5;
                result.trajectory_points[i].velocity[dof] = c1 + 2*c2*t + 3*c3*t2 + 4*c4*t3 + 5*c5*t4;
                result.trajectory_points[i].acceleration[dof] = 2*c2 + 6*c3*t + 12*c4*t2 + 20*c5*t3;
                result.trajectory_points[i].timestamp = t;
                result.trajectory_points[i].valid = true;
            }
        }

        result.duration = duration;
        result.success = true;
        
        std::cout << "Planned polynomial transition: duration = " 
                  << duration << "s, points = " 
                  << result.trajectory_points.size() << std::endl;

    } catch (const std::exception& e) {
        result.error_message = "Exception in polynomial planning: " + std::string(e.what());
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planEmergencyTransition(
    const MotionState<DOF>& current_state,
    const MotionState<DOF>& safe_state,
    double max_time) {
    
    TransitionResult<DOF> result;
    
    try {
        // 创建紧急停止状态（零速度）
        MotionState<DOF> emergency_state = safe_state;
        emergency_state.velocity = VectorDOF::Zero();
        emergency_state.acceleration = VectorDOF::Zero();
        
        // 临时放宽约束以实现快速响应
        auto original_constraints = constraints_;
        TransitionConstraints<DOF> emergency_constraints = constraints_;
        emergency_constraints.max_acceleration *= 2.0;  // 允许更大加速度
        emergency_constraints.max_jerk *= 3.0;          // 允许更大加加速度
        emergency_constraints.max_duration = max_time;
        
        setConstraints(emergency_constraints);
        
        // 使用时间最优规划
        result = planOptimalTimeTransition(current_state, emergency_state);
        
        // 恢复原始约束
        setConstraints(original_constraints);
        
        if (result.success && result.duration > max_time) {
            // 如果超时，使用线性过渡
            result = planLinearTransition(current_state, emergency_state, max_time);
        }
        
        std::cout << "Planned emergency transition: duration = " 
                  << result.duration << "s" << std::endl;

    } catch (const std::exception& e) {
        result.error_message = "Exception in emergency planning: " + std::string(e.what());
    }

    return result;
}

// 辅助方法实现
template<int DOF>
double TransitionPlanner<DOF>::estimateOptimalDuration(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state) const {

    double max_duration = 0.0;

    for (int i = 0; i < DOF; ++i) {
        double pos_diff = std::abs(to_state.position[i] - from_state.position[i]);
        double vel_diff = std::abs(to_state.velocity[i] - from_state.velocity[i]);

        // 基于位置差和最大速度估算
        double pos_time = pos_diff / constraints_.max_velocity[i];

        // 基于速度差和最大加速度估算
        double vel_time = vel_diff / constraints_.max_acceleration[i];

        // 取较大值
        double joint_time = std::max(pos_time, vel_time);
        max_duration = std::max(max_duration, joint_time);
    }

    // 添加安全裕度
    return std::max(max_duration * 1.2, constraints_.min_duration);
}

template<int DOF>
bool TransitionPlanner<DOF>::validateTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state) const {

    // 检查状态有效性
    for (int i = 0; i < DOF; ++i) {
        if (!std::isfinite(from_state.position[i]) || !std::isfinite(from_state.velocity[i]) ||
            !std::isfinite(to_state.position[i]) || !std::isfinite(to_state.velocity[i])) {
            return false;
        }

        // 检查速度约束
        if (std::abs(from_state.velocity[i]) > constraints_.max_velocity[i] ||
            std::abs(to_state.velocity[i]) > constraints_.max_velocity[i]) {
            return false;
        }
    }

    return true;
}

template<int DOF>
void TransitionPlanner<DOF>::evaluateTrajectoryQuality(TransitionResult<DOF>& result) const {
    if (result.trajectory_points.empty()) {
        return;
    }

    // 检查约束违反
    checkConstraintViolations(result.trajectory_points, result);

    // 计算平滑度分数（基于加加速度）
    double total_jerk = 0.0;
    for (size_t i = 1; i < result.trajectory_points.size(); ++i) {
        for (int j = 0; j < DOF; ++j) {
            double jerk = (result.trajectory_points[i].acceleration[j] -
                          result.trajectory_points[i-1].acceleration[j]) / dt_;
            total_jerk += std::abs(jerk);
        }
    }

    // 归一化平滑度分数（越小越好）
    result.smoothness_score = total_jerk / (result.trajectory_points.size() * DOF);
}

template<int DOF>
bool TransitionPlanner<DOF>::checkConstraintViolations(
    const std::vector<TrajectoryState<DOF>>& trajectory,
    TransitionResult<DOF>& result) const {

    bool has_violations = false;

    for (const auto& state : trajectory) {
        for (int i = 0; i < DOF; ++i) {
            // 检查速度约束
            double vel_violation = std::abs(state.velocity[i]) - constraints_.max_velocity[i];
            if (vel_violation > 0) {
                result.max_velocity_violation = std::max(result.max_velocity_violation, vel_violation);
                has_violations = true;
            }

            // 检查加速度约束
            double acc_violation = std::abs(state.acceleration[i]) - constraints_.max_acceleration[i];
            if (acc_violation > 0) {
                result.max_acceleration_violation = std::max(result.max_acceleration_violation, acc_violation);
                has_violations = true;
            }
        }
    }

    // 检查加加速度约束
    for (size_t i = 1; i < trajectory.size(); ++i) {
        for (int j = 0; j < DOF; ++j) {
            double jerk = std::abs((trajectory[i].acceleration[j] -
                                  trajectory[i-1].acceleration[j]) / dt_);
            double jerk_violation = jerk - constraints_.max_jerk[j];
            if (jerk_violation > 0) {
                result.max_jerk_violation = std::max(result.max_jerk_violation, jerk_violation);
                has_violations = true;
            }
        }
    }

    return !has_violations;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planSplineTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state,
    double duration) {

    TransitionResult<DOF> result;

    // 简化实现：使用多项式过渡作为样条的近似
    // 实际实现可以使用更复杂的样条插值
    result = planPolynomialTransition(from_state, to_state, duration);

    if (result.success) {
        std::cout << "Planned spline transition (polynomial approximation): duration = "
                  << duration << "s" << std::endl;
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::planSmoothJerkTransition(
    const MotionState<DOF>& from_state,
    const MotionState<DOF>& to_state,
    double duration) {

    TransitionResult<DOF> result;

    // 使用Ruckig规划器，它本身就优化了加加速度
    result = planOptimalTimeTransition(from_state, to_state);

    // 如果需要特定持续时间，进行时间缩放
    if (result.success && duration > 0 && std::abs(result.duration - duration) > 0.01) {
        double scale_factor = duration / result.duration;

        // 重新缩放时间戳
        for (size_t i = 0; i < result.timestamps.size(); ++i) {
            result.timestamps[i] *= scale_factor;
        }
        result.duration = duration;

        std::cout << "Scaled smooth jerk transition to duration = "
                  << duration << "s" << std::endl;
    }

    return result;
}

template<int DOF>
TransitionResult<DOF> TransitionPlanner<DOF>::smoothTrajectory(
    const std::vector<TrajectoryState<DOF>>& input_trajectory,
    double smoothing_window) {

    TransitionResult<DOF> result;

    if (input_trajectory.empty()) {
        result.error_message = "Empty input trajectory";
        return result;
    }

    try {
        // 应用高斯平滑
        result.trajectory_points = applyGaussianSmoothing(input_trajectory, smoothing_window);

        // 重新计算时间戳
        result.timestamps.clear();
        result.timestamps.reserve(result.trajectory_points.size());
        for (size_t i = 0; i < result.trajectory_points.size(); ++i) {
            result.timestamps.push_back(i * dt_);
        }

        if (!result.trajectory_points.empty()) {
            result.duration = result.timestamps.back();
        }

        result.success = true;

        std::cout << "Smoothed trajectory with " << result.trajectory_points.size()
                  << " points" << std::endl;

    } catch (const std::exception& e) {
        result.error_message = "Exception in trajectory smoothing: " + std::string(e.what());
    }

    return result;
}

template<int DOF>
std::vector<TrajectoryState<DOF>> TransitionPlanner<DOF>::applyGaussianSmoothing(
    const std::vector<TrajectoryState<DOF>>& trajectory,
    double sigma) const {

    if (trajectory.size() < 3) {
        return trajectory;  // 太短无法平滑
    }

    std::vector<TrajectoryState<DOF>> smoothed = trajectory;
    int window_size = static_cast<int>(3 * sigma / dt_);  // 3σ窗口
    window_size = std::max(3, std::min(window_size, static_cast<int>(trajectory.size()) / 2));

    // 生成高斯核
    std::vector<double> kernel(window_size);
    double sum = 0.0;
    int center = window_size / 2;

    for (int i = 0; i < window_size; ++i) {
        double x = (i - center) * dt_;
        kernel[i] = std::exp(-x * x / (2 * sigma * sigma));
        sum += kernel[i];
    }

    // 归一化核
    for (double& k : kernel) {
        k /= sum;
    }

    // 应用平滑
    for (size_t i = center; i < trajectory.size() - center; ++i) {
        TrajectoryState<DOF> smoothed_state;
        smoothed_state.position = VectorDOF::Zero();
        smoothed_state.velocity = VectorDOF::Zero();
        smoothed_state.acceleration = VectorDOF::Zero();
        smoothed_state.timestamp = trajectory[i].timestamp;
        smoothed_state.valid = true;

        for (int k = 0; k < window_size; ++k) {
            size_t idx = i - center + k;
            smoothed_state.position += kernel[k] * trajectory[idx].position;
            smoothed_state.velocity += kernel[k] * trajectory[idx].velocity;
            smoothed_state.acceleration += kernel[k] * trajectory[idx].acceleration;
        }

        smoothed[i] = smoothed_state;
    }

    return smoothed;
}

// 显式实例化
template class TransitionPlanner<6>;
template class TransitionPlanner<7>;
