#include "OnlineTracker.h"

int main() {
    try {
        OnlineTracker tracker("robot_utils/panda-model/panda_arm.urdf");
        
        // 设置初始状态（可选）
        Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);
        Eigen::VectorXd dq_init = Eigen::VectorXd::Zero(7);
        tracker.setInitialState(q_init, dq_init);
        
        // 启动跟踪器
        tracker.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
} 