#pragma once

#include <string>
#include <vector>
#include <memory>
#include <Eigen/Dense>
#include <pinocchio/parsers/urdf.hpp>
#include <pinocchio/algorithm/kinematics.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/jacobian.hpp>
#include <pinocchio/algorithm/joint-configuration.hpp>
#include <pinocchio/algorithm/rnea.hpp>
#include <pinocchio/algorithm/crba.hpp>
#include <pinocchio/algorithm/aba.hpp>


namespace robot_infra {

/**
 * @brief 机器人模型类
 * 
 * 使用Pinocchio库读取URDF文件，提供正逆运动学、关节限位、位置限位等功能
 */
class RobotModel {
public:
    using Vector6d = Eigen::Matrix<double, 6, 1>;
    using Vector7d = Eigen::Matrix<double, 7, 1>;
    using VectorXd = Eigen::VectorXd;
    using Matrix3d = Eigen::Matrix3d;
    using Matrix6d = Eigen::Matrix<double, 6, 6>;
    using MatrixXd = Eigen::MatrixXd;
    using SE3 = pinocchio::SE3;

    /**
     * @brief 构造函数
     * @param urdf_path URDF文件路径
     * @param end_effector_frame_name 末端执行器坐标系名称
     */
    explicit RobotModel(const std::string& urdf_path, 
                       const std::string& end_effector_frame_name = "");

    /**
     * @brief 析构函数
     */
    ~RobotModel() = default;

    // ============================================================================
    // 初始化和配置
    // ============================================================================
    
    /**
     * @brief 初始化机器人模型
     * @return 是否成功初始化
     */
    bool initialize();

    /**
     * @brief 设置关节限位
     * @param q_min 关节最小值
     * @param q_max 关节最大值
     * @param dq_max 关节速度最大值
     * @param ddq_max 关节加速度最大值
     */
    void setJointLimits(const VectorXd& q_min, const VectorXd& q_max,
                       const VectorXd& dq_max, const VectorXd& ddq_max);

    /**
     * @brief 设置工作空间限位
     * @param workspace_min 工作空间最小值 [x, y, z, rx, ry, rz]待定
     * @param workspace_max 工作空间最大值 [x, y, z, rx, ry, rz]
     */
    void setWorkspaceLimits(const Vector6d& workspace_min, 
                           const Vector6d& workspace_max);


    VectorXd generatePositionUniform() const;

    /**
     * @brief 检查关节角度是否在有效范围内
     * @param q 关节角度向量
     * @return 如果所有关节都在有效范围内返回true，否则返回false
     */
    bool isValid(const VectorXd& q) const;

    /**
     * @brief 计算两个关节配置之间的变换距离
     * @param q1 第一个关节配置
     * @param q2 第二个关节配置
     * @return 变换距离的平方
     */
    double transformedDistance(const VectorXd& q1, const VectorXd& q2) const;

    /**
     * @brief 在两个关节配置之间进行插值
     * @param q1 起始关节配置
     * @param q2 目标关节配置
     * @param delta 最大步长（当距离超过此值时进行插值）
     * @param q_result 插值结果
     */
    void interpolate(const VectorXd& q1, const VectorXd& q2, double delta, VectorXd& q_result) const;

    // ============================================================================
    // 正运动学
    // ============================================================================
    
    /**
     * @brief 正运动学计算
     * @param q 关节角度
     * @return 末端执行器位姿 [x, y, z, rz, ry, rx]
     */
    Vector6d forwardKinematics(const VectorXd& q);

    /**
     * @brief 获取末端执行器变换矩阵
     * @param q 关节角度
     * @return SE3变换矩阵
     */
    SE3 getEndEffectorTransform(const VectorXd& q);

    /**
     * @brief 计算雅可比矩阵
     * @param q 关节角度
     * @param frame 参考坐标系 (LOCAL_WORLD_ALIGNED 或 LOCAL)
     * @return 6xN雅可比矩阵
     */
    MatrixXd computeJacobian(const VectorXd& q,
                            pinocchio::ReferenceFrame frame = pinocchio::LOCAL_WORLD_ALIGNED);

    /**
     * @brief 更新雅可比伪逆矩阵
     * @param jacobian 雅可比矩阵
     * @param lambda 阻尼参数
     * @param doSvd 是否使用SVD方法
     * @return 雅可比伪逆矩阵
     */
    MatrixXd updateJacobianInverse(const MatrixXd& jacobian,
                                  const double lambda = 0,
                                  bool doSvd = true);

    // ============================================================================
    // 逆运动学
    // ============================================================================
    
    /**
     * @brief 逆运动学计算 (数值解法)
     * @param q_result 求解结果的关节角度（通过引用返回）
     * @param target_pose 目标位姿 [x, y, z, rz, ry, rx] (ZYX欧拉角)
     * @param q_init 初始关节角度
     * @param max_iterations 最大迭代次数
     * @param tolerance 收敛容差
     * @return 是否成功求解
     */
    bool inverseKinematics(VectorXd& q_result,
                          const Vector6d& target_pose,
                          const VectorXd& q_init,
                          int max_iterations = 100,
                          double tolerance = 1e-4);



    // ============================================================================
    // 动力学
    // ============================================================================
    
    /**
     * @brief 计算惯性矩阵
     * @param q 关节角度
     * @return 惯性矩阵
     */
    MatrixXd computeInertiaMatrix(const VectorXd& q);

    /**
     * @brief 计算科里奥利力和离心力
     * @param q 关节角度
     * @param dq 关节速度
     * @return 科里奥利力和离心力
     */
    VectorXd computeCoriolisForces(const VectorXd& q, const VectorXd& dq);

    /**
     * @brief 计算重力项
     * @param q 关节角度
     * @return 重力项
     */
    VectorXd computeGravityForces(const VectorXd& q);

    /**
     * @brief 逆动力学计算
     * @param q 关节角度
     * @param dq 关节速度
     * @param ddq 关节加速度
     * @return 关节力矩
     */
    VectorXd inverseDynamics(const VectorXd& q, const VectorXd& dq, const VectorXd& ddq);

    /**
     * @brief 正动力学计算
     * @param q 关节角度
     * @param dq 关节速度
     * @param tau 关节力矩
     * @return 关节加速度
     */
    VectorXd forwardDynamics(const VectorXd& q, const VectorXd& dq, const VectorXd& tau);

    // ============================================================================
    // 限位检查
    // ============================================================================
    
    /**
     * @brief 检查关节限位
     * @param q 关节角度
     * @return 是否在限位内
     */
    bool checkJointLimits(const VectorXd& q) const;

    /**
     * @brief 检查关节速度限位
     * @param dq 关节速度
     * @return 是否在限位内
     */
    bool checkJointVelocityLimits(const VectorXd& dq) const;

    /**
     * @brief 检查关节加速度限位
     * @param ddq 关节加速度
     * @return 是否在限位内
     */
    bool checkJointAccelerationLimits(const VectorXd& ddq) const;

    /**
     * @brief 检查工作空间限位
     * @param pose 末端执行器位姿 [x, y, z, rx, ry, rz]
     * @return 是否在工作空间内
     */
    bool checkWorkspaceLimits(const Vector6d& pose) const;

    /**
     * @brief 将关节角度限制在限位内
     * @param q 关节角度
     * @return 限制后的关节角度
     */
    VectorXd clampJointLimits(const VectorXd& q) const;

    /**
     * @brief 归一化关节角度并应用关节限位
     * @param q 关节角度
     * @return 归一化并限制后的关节角度
     */
    VectorXd normalizeAndLimitJoints(const VectorXd& q) const;

    /**
     * @brief 将关节速度限制在限位内
     * @param dq 关节速度
     * @return 限制后的关节速度
     */
    VectorXd clampJointVelocityLimits(const VectorXd& dq) const;

    /**
     * @brief 将工作空间位姿限制在限位内
     * @param pose 末端执行器位姿 [x, y, z, rx, ry, rz]
     * @return 限制后的位姿
     */
    Vector6d clampWorkspaceLimits(const Vector6d& pose) const;

    // ============================================================================
    // 统一限位检查和处理方法
    // ============================================================================

    /**
     * @brief 验证并夹紧关节指令
     * @param jointRad 关节角度
     * @param command_name 指令名称（用于日志）
     * @return 是否成功处理
     */
    bool validateAndClampJointCommand(VectorXd& jointRad, const std::string& command_name) const;

    /**
     * @brief 验证并夹紧笛卡尔指令
     * @param pose 末端执行器位姿
     * @param command_name 指令名称（用于日志）
     * @return 是否成功处理
     */
    bool validateAndClampCartesianCommand(Vector6d& pose, const std::string& command_name) const;

    // ============================================================================
    // 获取器方法
    // ============================================================================
    
    /**
     * @brief 获取关节数量
     * @return 关节数量
     */
    int getJointCount() const { return nq_; }

    /**
<<<<<<< HEAD
     * @brief 获取机器人名称
     * @return 机器人名称（来自URDF根节点）
     */
    std::string getRobotName() const;

    /**
=======
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
     * @brief 获取关节位置限位
     * @return std::pair<VectorXd, VectorXd> (最小值, 最大值)
     */
    std::pair<VectorXd, VectorXd> getJointLimits() const;

    /**
     * @brief 获取关节速度限位
     * @return 关节速度最大值
     */
    VectorXd getJointVelocityLimits() const { return dq_max_; }

    /**
     * @brief 获取关节加速度限位
     * @return 关节加速度最大值
     */
    VectorXd getJointAccelerationLimits() const { return ddq_max_; }

    /**
     * @brief 获取工作空间限位
     * @return std::pair<Vector6d, Vector6d> (最小值, 最大值)
     */
    std::pair<Vector6d, Vector6d> getWorkspaceLimits() const;

    /**
     * @brief 获取模型信息
     * @return 模型信息字符串
     */
    std::string getModelInfo() const;

private:
    // Pinocchio模型和数据
    pinocchio::Model model_;
    pinocchio::Data data_;
    
    // 模型参数
    std::string urdf_path_;
    std::string end_effector_frame_name_;
    pinocchio::FrameIndex end_effector_frame_id_;
    int nq_;  // 关节数量
    
    // 关节限位
    VectorXd q_min_, q_max_;        // 关节位置限位
    VectorXd dq_max_;               // 关节速度限位
    VectorXd ddq_max_;              // 关节加速度限位
    
    // 工作空间限位
    Vector6d workspace_min_, workspace_max_;
    bool workspace_limits_set_;
    
    // 初始化标志
    bool initialized_;
    
    /**
     * @brief 检查初始化状态和向量尺寸
     * @param q 关节向量
     */
    void checkInitAndSize(const VectorXd& q) const;

    /**
     * @brief 将可动关节配置映射到完整模型配置
     * @param q_active 可动关节配置
     * @return 完整模型配置
     */
    VectorXd mapToFullConfiguration(const VectorXd& q_active) const;





    /**
     * @brief 将SE3变换转换为6D位姿向量
     * @param transform SE3变换
     * @return 6D位姿向量 [x, y, z, rx, ry, rz]
     */
    Vector6d se3ToPose(const SE3& transform) const;
    
    /**
     * @brief 将6D位姿向量转换为SE3变换
     * @param pose 6D位姿向量 [x, y, z, rx, ry, rz]
     * @return SE3变换
     */
    SE3 poseToSE3(const Vector6d& pose) const;
    
    /**
     * @brief 计算位姿误差
     * @param current_transform 当前变换
     * @param target_transform 目标变换
     * @return 6D误差向量
     */
    Vector6d computePoseError(const SE3& current_transform, 
                             const SE3& target_transform) const;
};

} // namespace robot_infra
