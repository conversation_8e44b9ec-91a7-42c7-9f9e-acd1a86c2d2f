#ifndef TOPPRA_INTERPOLATOR_H
#define TOPPRA_INTERPOLATOR_H


#include <vector>
#include <memory>
#include <cmath>
#include <iostream>

#include "TrajInterpolatorBase.h"

#include <toppra/algorithm/toppra.hpp>
#include <toppra/constraint/linear_joint_acceleration.hpp>
#include <toppra/constraint/linear_joint_velocity.hpp>
#include <toppra/parametrizer/spline.hpp>
#include <toppra/geometric_path/piecewise_poly_path.hpp>
#include <toppra/parametrizer/const_accel.hpp>
// #include <toppra/constraint/joint_torque/pinocchio.hpp>
// #include <toppra/solver/qpOASES-wrapper.hpp>
// #include <toppra/solver/glpk-wrapper.hpp>
#include <toppra/solver/seidel.hpp>
#include <toppra/toppra.hpp>

#include <Eigen/Dense>


// Toppra 插补实现

template <const int N>
class ToppraInterpolator : public TrajInterpolator<N> {
public:
    using typename TrajInterpolator<N>::VectorNd;
    using typename TrajInterpolator<N>::MatrixNd;

    ToppraInterpolator(double control_period, const std::string& urdf_path)
        : TrajInterpolator<N>(control_period, urdf_path) {}

    void computeOffline(const std::vector<VectorNd>& waypoints) override {
        // 构建路径
        toppra::Vectors positions;
        for (const auto& wp : waypoints) {
            positions.push_back(wp);
        }
        toppra::Vector times(positions.size());
        times.setLinSpaced(0, 1);
        std::array<toppra::BoundaryCond, 2> boundary_cond;
        toppra::BoundaryCond bc{"clamped"};
        boundary_cond[0] = bc;
        boundary_cond[1] = bc;
        auto path_obj = toppra::PiecewisePolyPath::CubicSpline(positions, times, boundary_cond);
        path = std::make_shared<toppra::PiecewisePolyPath>(path_obj);

        // 构建约束
        toppra::LinearConstraintPtrs constraints;
        constraints.push_back(std::make_shared<toppra::constraint::LinearJointVelocity>(-this->m_max_vel, this->m_max_vel));
        constraints.push_back(std::make_shared<toppra::constraint::LinearJointAcceleration>(-this->m_max_acc, this->m_max_acc));

        // 构建 TOPPRA 算法
        toppra::algorithm::TOPPRA algo(constraints, path);
        algo.solver(std::make_shared<toppra::solver::Seidel>());
        algo.computePathParametrization(0, 0);
        data = algo.getParameterizationData();
        trajectory = std::make_shared<toppra::parametrizer::ConstAccel>(path, data.gridpoints, data.parametrization);
    }

    bool computeOnline(const VectorNd&, const VectorNd&, const VectorNd&,
                       const VectorNd&, const VectorNd&, const VectorNd&, double) override {
        return false;
    }

    void sample(double dt,
                std::vector<double>& ts,
                MatrixNd& pos,
                MatrixNd& vel,
                MatrixNd& acc) const override {
        if (!trajectory) throw std::runtime_error("Trajectory not yet computed.");
        auto path_interval = trajectory->pathInterval();
        double duration = path_interval[1] - path_interval[0];
        int Np = static_cast<int>(std::ceil(duration / dt)) + 1;
        ts.resize(Np);
        pos.resize(N, Np);
        vel.resize(N, Np);
        acc.resize(N, Np);
        Eigen::VectorXd u(1);
        for (int i = 0; i < Np; ++i) {
            double t = std::min(i * dt, duration);
            ts[i] = t;
            u(0) = t;
            pos.col(i) = trajectory->eval(u, 0)[0];
            vel.col(i) = trajectory->eval(u, 1)[0];
            acc.col(i) = trajectory->eval(u, 2)[0];
        }
    }

private:
    std::shared_ptr<toppra::GeometricPath> path;
    toppra::ParametrizationData data;
    std::shared_ptr<toppra::parametrizer::ConstAccel> trajectory;
};

#endif // TOPPRA_INTERPOLATOR_H 