cmake_minimum_required(VERSION 3.10)

find_package(Eigen3 REQUIRED)
find_package(ruckig QUIET)
find_package(toppra QUIET)
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZMQ REQUIRED libzmq)

# 自动查找并编译所有.cpp文件
file(GLOB EXAMPLE_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")

foreach(example_src ${EXAMPLE_SOURCES})
    get_filename_component(example_name ${example_src} NAME_WE)

    # 特殊处理需要额外依赖的示例
    if(example_name STREQUAL "ruckig_example")
        if(ruckig_FOUND)
            add_executable(${example_name} ${example_src})
            target_include_directories(${example_name} PRIVATE ${CMAKE_SOURCE_DIR}/include)
            target_link_libraries(${example_name} PRIVATE ruckig::ruckig Eigen3::Eigen)
            target_compile_features(${example_name} PRIVATE cxx_std_17)
            message(STATUS "Added example: ${example_name} (with ruckig)")
        else()
            message(STATUS "Skipped example: ${example_name} (ruckig not found)")
        endif()
    elseif(example_name STREQUAL "toppra_example")
        if(toppra_FOUND)
            add_executable(${example_name} ${example_src})
            target_include_directories(${example_name} PRIVATE ${CMAKE_SOURCE_DIR}/include)
            target_link_libraries(${example_name} PRIVATE toppra::toppra Eigen3::Eigen)
            target_compile_features(${example_name} PRIVATE cxx_std_17)
            message(STATUS "Added example: ${example_name} (with toppra)")
        else()
            message(STATUS "Skipped example: ${example_name} (toppra not found)")
        endif()
    elseif(example_name STREQUAL "hybrid_trajectory_example")
        if(ruckig_FOUND AND toppra_FOUND)
            add_executable(${example_name} ${example_src})
            target_include_directories(${example_name} PRIVATE ${CMAKE_SOURCE_DIR}/include)
            target_link_libraries(${example_name} PRIVATE
                robot_infra
                ruckig::ruckig
                toppra::toppra
                Eigen3::Eigen
                pthread
            )
            target_compile_features(${example_name} PRIVATE cxx_std_17)
            message(STATUS "Added example: ${example_name} (with hybrid trajectory support)")
        else()
            message(STATUS "Skipped example: ${example_name} (ruckig or toppra not found)")
        endif()
    else()
        # 通用示例：链接robot_infra库
        add_executable(${example_name} ${example_src})
        target_include_directories(${example_name} PRIVATE ${CMAKE_SOURCE_DIR}/include)
        target_link_libraries(${example_name} PRIVATE robot_infra Eigen3::Eigen)
        target_compile_features(${example_name} PRIVATE cxx_std_17)
        message(STATUS "Added example: ${example_name}")
    endif()
endforeach()

