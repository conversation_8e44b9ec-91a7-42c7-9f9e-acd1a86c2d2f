#ifndef FIFO_COMM_H
#define FIFO_COMM_H

#include "CommBase.h"
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <cerrno>
#include <cstring>
#include <iostream>
#include <functional>

class FIFOComm : public CommBase {
public:
    explicit FIFOComm(bool is_server = false);
    ~FIFOComm();
    // 只保留 servo command 相关接口
    bool init() override;
    void close() override;
    bool isConnected() const override;
    // sendRtData/recvRtData 采用字符串格式：M double[6]pos double[6]vec double time
    bool sendRtData(const ServoCommand& cmd) override;
    bool recvRtData(const std::function<void(const ServoCommand&)>& callback) override;
    // 其他接口全部屏蔽
    std::string requestReply(const std::string& cmd) override { return ""; }
    bool sendRtData(const RobotState& state) override { return false; }
    bool recvRtData(const std::function<void(const RobotState&)>& callback) override { return false; }
    std::string recvReply() override { return ""; }
    bool sendFeedbackData(const std::string&) override { return false; }
    bool recvFeedbackData(const std::function<void(const std::string&)>&) override { return false; }
    uint64_t getCurrentTimestamp() const override;
    std::string getConnectionInfo() const override { return "FIFO: servo only"; }
private:
    bool is_server_ = false;
#ifdef DEBUG
    static constexpr const char* data_stream_fifo  = "/tmp/test_data_stream_fifo";//test_data_stream_fifo
#else
    static constexpr const char* data_stream_fifo  = "/tmp/cmd_fifo";//data_stream_fifo
#endif
    int fd_data_stream_{-1};
    ssize_t read_full(int fd, char* buf, size_t len);
};

#endif // FIFO_COMM_H 