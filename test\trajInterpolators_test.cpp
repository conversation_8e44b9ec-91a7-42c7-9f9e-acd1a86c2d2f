#include "../include/trajectory/ToppraInterpolator.hpp"
#include "../include/trajectory/RuckigInterpolator.hpp"
#include <iostream>
#include <vector>

// Simple test to verify the interpolators compile correctly
int main() {
    constexpr int DOF = 7;
    
    // Test ToppraInterpolator
    {
        ToppraInterpolator<DOF> toppra_interp(0.001, "");
        
        // Set constraints
        MotionConstraints<DOF> constraints;
        constraints.max_velocity.setOnes() * 1.0;
        constraints.max_acceleration.setOnes() * 2.0;
        constraints.max_jerk.setOnes() * 10.0;
        toppra_interp.setConstraints(constraints);
        
        // Create waypoints
        std::vector<MotionState<DOF>> waypoints;
        
        MotionState<DOF> start;
        start.position << 0.1,0.2,0.3,0,0,0,0;
        start.velocity.setZero();
        start.acceleration.setZero();
        waypoints.push_back(start);
        
        MotionState<DOF> end;
        end.position << 1,2,3,0,0,0,0;
        end.velocity.setZero();
        end.acceleration.setZero();
        waypoints.push_back(end);
        
        // Test offline computation
        bool success = toppra_interp.computeOffline(waypoints);
        std::cout << "ToppraInterpolator offline computation: " 
                  << (success ? "SUCCESS" : "FAILED") << std::endl;
        if (!success) {
            std::cout << "Error: " << toppra_interp.getLastError() << std::endl;
        }
        
        // Test online computation (should fail for TOPPRA)
        success = toppra_interp.computeOnline(start, end);
        std::cout << "ToppraInterpolator online computation: " 
                  << (success ? "SUCCESS" : "FAILED (expected)") << std::endl;

        const auto& trajectory_buffer = toppra_interp.getTrajectoryBuffer();

        // Debug information
        std::cout << "Debug: Buffer size = " << trajectory_buffer.size() << std::endl;
        std::cout << "Debug: Duration = " << trajectory_buffer.getDuration() << std::endl;
    
        trajectory_buffer.sampleAndPrint(0.01, 50, 4);  // dt=0.01s, max 50 samples, 4 decimal places
    }
    
    // Test RuckigInterpolator
    {
        RuckigInterpolator<DOF> ruckig_interp(0.001, "");
        
        // Set constraints
        MotionConstraints<DOF> constraints;
        constraints.max_velocity.setOnes() * 1.0;
        constraints.max_acceleration.setOnes() * 2.0;
        constraints.max_jerk.setOnes() * 10.0;
        ruckig_interp.setConstraints(constraints);
        
        // Create states
        MotionState<DOF> start;
        start.position.setZero();
        start.velocity.setZero();
        start.acceleration.setZero();
        
        MotionState<DOF> end;
        end.position.setOnes();
        end.velocity.setZero();
        end.acceleration.setZero();
        
        // Test online computation
        bool success = ruckig_interp.computeOnline(start, end);
        std::cout << "RuckigInterpolator online computation: " 
                  << (success ? "SUCCESS" : "FAILED") << std::endl;
        if (!success) {
            std::cout << "Error: " << ruckig_interp.getLastError() << std::endl;
        }
        
        // Test offline computation
        std::vector<MotionState<DOF>> waypoints = {start, end};
        success = ruckig_interp.computeOffline(waypoints);
        std::cout << "RuckigInterpolator offline computation: " 
                  << (success ? "SUCCESS" : "FAILED") << std::endl;
        if (!success) {
            std::cout << "Error: " << ruckig_interp.getLastError() << std::endl;
        }
        
        const auto& trajectory_buffer = ruckig_interp.getTrajectoryBuffer();

        // Debug information
        std::cout << "Debug: Buffer size = " << trajectory_buffer.size() << std::endl;
        std::cout << "Debug: Duration = " << trajectory_buffer.getDuration() << std::endl;
    
        trajectory_buffer.sampleAndPrint(0.01, 50, 4);  // dt=0.01s, max 50 samples, 4 decimal places
    }
    
    return 0;
}
