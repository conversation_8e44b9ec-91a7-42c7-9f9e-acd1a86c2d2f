#include <iostream>
#include <robot_infra/industRob.h>

int main() {
    std::cout << "=== robot_infra 库使用示例 ===" << std::endl;
    
    try {
        // 创建机器人实例
        industRob robot(CommType::ZMQ);
        
        std::cout << "机器人实例创建成功" << std::endl;
        std::cout << "连接信息: " << robot.getConnectionInfo() << std::endl;
        
        // 测试一些基本功能
        std::vector<double> test_joints = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
        auto fk_result = robot.forwardKinematics(test_joints);
        
        std::cout << "正运动学测试结果: [";
        for (size_t i = 0; i < fk_result.size(); ++i) {
            std::cout << fk_result[i];
            if (i < fk_result.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
        
        std::cout << "库功能测试完成!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
