#ifndef TOPPRA_INTERPOLATOR_H
#define TOPPRA_INTERPOLATOR_H

#include <vector>
#include <memory>
#include <cmath>
#include <iostream>

#include "TrajInterpolatorBase.hpp"

#include <toppra/algorithm/toppra.hpp>
#include <toppra/constraint/linear_joint_acceleration.hpp>
#include <toppra/constraint/linear_joint_velocity.hpp>
#include <toppra/parametrizer/spline.hpp>
#include <toppra/geometric_path/piecewise_poly_path.hpp>
#include <toppra/parametrizer/const_accel.hpp>
#include <toppra/solver/seidel.hpp>
#include <toppra/toppra.hpp>


#include <Eigen/Dense>

// TOPPRA trajectory interpolator implementation
// 路径插值方法枚举
enum class PathInterpolationMethod {
    CUBIC_SPLINE,           // 三次样条（默认，平滑但可能超出途经点）
    CUBIC_HERMITE_SPLINE,   // 三次Hermite样条（需要速度信息，严格通过途经点）
    LINEAR_SEGMENTS         // 分段线性（严格通过途经点，但不平滑）
};

// 边界条件类型
enum class BoundaryConditionType {
    CLAMPED,        // 固定边界（首末速度为0）
    NATURAL,        // 自然边界（首末加速度为0）
    NOT_A_KNOT      // 非节点边界（默认，最平滑）
};

template <int DOF>
class ToppraInterpolator : public TrajectoryInterpolator<DOF> {
public:
    using VectorDOF = typename TrajectoryInterpolator<DOF>::VectorDOF;
    using MatrixDOF = typename TrajectoryInterpolator<DOF>::MatrixDOF;

    ToppraInterpolator(double dt = 0.001, const std::string& urdf_path = "")
        : TrajectoryInterpolator<DOF>(dt, urdf_path),
          interpolation_method_(PathInterpolationMethod::CUBIC_SPLINE),
          boundary_condition_(BoundaryConditionType::CLAMPED) {}

    // 设置路径插值方法
    void setInterpolationMethod(PathInterpolationMethod method) {
        interpolation_method_ = method;
    }

    // 设置边界条件
    void setBoundaryCondition(BoundaryConditionType bc_type) {
        boundary_condition_ = bc_type;
    }

    // 获取当前插值方法
    PathInterpolationMethod getInterpolationMethod() const {
        return interpolation_method_;
    }

    // Offline trajectory planning (multiple waypoints)
    bool computeOffline(const std::vector<MotionState<DOF>>& waypoints) override {
        this->clearError();

        if (waypoints.empty()) {
            this->setError("Empty waypoints provided");
            return false;
        }

        try {
            // Extract positions and velocities from MotionState waypoints
            toppra::Vectors positions;
            toppra::Vectors velocities;
            for (const auto& wp : waypoints) {
                positions.push_back(wp.position);
                velocities.push_back(wp.velocity);
            }

            // Create time vector
            toppra::Vector times(positions.size());
            times.setLinSpaced(0, 1);

            // Create path based on selected interpolation method
            path_ = createPath(positions, velocities, times);

            // Build constraints using the new constraint structure
            toppra::LinearConstraintPtrs constraints;
            constraints.push_back(std::make_shared<toppra::constraint::LinearJointVelocity>(
                -this->constraints_.max_velocity, this->constraints_.max_velocity));
            constraints.push_back(std::make_shared<toppra::constraint::LinearJointAcceleration>(
                -this->constraints_.max_acceleration, this->constraints_.max_acceleration));

            // Build TOPPRA algorithm
            toppra::algorithm::TOPPRA algo(constraints, path_);
            algo.solver(std::make_shared<toppra::solver::Seidel>());
            algo.computePathParametrization(0, 0);
            data_ = algo.getParameterizationData();
            trajectory_ = std::make_shared<toppra::parametrizer::ConstAccel>(path_, data_.gridpoints, data_.parametrization);

            // Sample trajectory and store in buffer
            return sampleTrajectoryToBuffer();

        } catch (const std::exception& e) {
            this->setError("TOPPRA computation failed: " + std::string(e.what()));
            return false;
        }
    }

    // Online trajectory planning (TOPPRA is primarily an offline algorithm)
    bool computeOnline(const MotionState<DOF>& current_state,
                       const MotionState<DOF>& target_state) override {
        this->setError("TOPPRA is an offline algorithm. Use computeOffline() instead.");
        return false;
    }

    // Get interpolator type name
    std::string getTypeName() const override {
        std::string method_name;
        switch (interpolation_method_) {
            case PathInterpolationMethod::CUBIC_HERMITE_SPLINE:
                method_name = "CubicHermite";
                break;
            case PathInterpolationMethod::LINEAR_SEGMENTS:
                method_name = "Linear";
                break;
            case PathInterpolationMethod::CUBIC_SPLINE:
            default:
                method_name = "CubicSpline";
                break;
        }
        return "ToppraInterpolator_" + method_name;
    }

private:
    PathInterpolationMethod interpolation_method_;
    BoundaryConditionType boundary_condition_;

    std::shared_ptr<toppra::GeometricPath> path_;
    toppra::ParametrizationData data_;
    std::shared_ptr<toppra::parametrizer::ConstAccel> trajectory_;

    // 创建路径的统一接口
    std::shared_ptr<toppra::PiecewisePolyPath> createPath(
        const toppra::Vectors& positions,
        const toppra::Vectors& velocities,
        const toppra::Vector& times) {

        switch (interpolation_method_) {
            case PathInterpolationMethod::CUBIC_HERMITE_SPLINE:
                return createCubicHermitePath(positions, velocities, times);

            case PathInterpolationMethod::LINEAR_SEGMENTS:
                return createLinearPath(positions, times);

            case PathInterpolationMethod::CUBIC_SPLINE:
            default:
                return createCubicSplinePath(positions, times);
        }
    }

    // 创建三次样条路径（默认方法，平滑但可能超出途经点）
    std::shared_ptr<toppra::PiecewisePolyPath> createCubicSplinePath(
        const toppra::Vectors& positions,
        const toppra::Vector& times) {

        // 设置边界条件
        std::array<toppra::BoundaryCond, 2> boundary_cond;
        std::string bc_type;
        switch (boundary_condition_) {
            case BoundaryConditionType::NATURAL:
                bc_type = "natural";
                break;
            case BoundaryConditionType::NOT_A_KNOT:
                bc_type = "not-a-knot";
                break;
            case BoundaryConditionType::CLAMPED:
            default:
                bc_type = "clamped";
                break;
        }

        toppra::BoundaryCond bc{bc_type};
        boundary_cond[0] = bc;
        boundary_cond[1] = bc;

        auto path_obj = toppra::PiecewisePolyPath::CubicSpline(positions, times, boundary_cond);
        return std::make_shared<toppra::PiecewisePolyPath>(path_obj);
    }

    // 创建三次Hermite样条路径（严格通过途经点，需要速度信息）
    std::shared_ptr<toppra::PiecewisePolyPath> createCubicHermitePath(
        const toppra::Vectors& positions,
        const toppra::Vectors& velocities,
        const toppra::Vector& times) {

        // 转换时间向量为std::vector
        std::vector<toppra::value_type> time_vec(times.data(), times.data() + times.size());

        auto path_obj = toppra::PiecewisePolyPath::CubicHermiteSpline(positions, velocities, time_vec);
        return std::make_shared<toppra::PiecewisePolyPath>(path_obj);
    }

    // 创建分段线性路径（严格通过途经点，但不平滑）
    std::shared_ptr<toppra::PiecewisePolyPath> createLinearPath(
        const toppra::Vectors& positions,
        const toppra::Vector& times) {

        // 为线性插值创建零速度
        toppra::Vectors zero_velocities;
        for (size_t i = 0; i < positions.size(); ++i) {
            toppra::Vector zero_vel = toppra::Vector::Zero(positions[i].size());
            zero_velocities.push_back(zero_vel);
        }

        // 转换时间向量
        std::vector<toppra::value_type> time_vec(times.data(), times.data() + times.size());

        // 使用Hermite样条但速度为0来实现线性插值
        auto path_obj = toppra::PiecewisePolyPath::CubicHermiteSpline(positions, zero_velocities, time_vec);
        return std::make_shared<toppra::PiecewisePolyPath>(path_obj);
    }


    // Sample the computed trajectory and store in trajectory buffer
    bool sampleTrajectoryToBuffer() {

        if (!trajectory_) {
            this->setError("No trajectory computed");
            return false;
        }

        try {
            auto path_interval = trajectory_->pathInterval();
            double duration = path_interval[1] - path_interval[0];

            // Sample at the interpolator's time step
            int num_samples = static_cast<int>(std::ceil(duration / this->dt_)) + 1;

            std::vector<TrajectoryState<DOF>> states;
            std::vector<double> timestamps;

            states.reserve(num_samples);
            timestamps.reserve(num_samples);

            Eigen::VectorXd u(1);
            for (int i = 0; i < num_samples; ++i) {
                double t = std::min(i * this->dt_, duration);
                u(0) = t;

                TrajectoryState<DOF> state;
                state.position = trajectory_->eval(u, 0)[0];
                state.velocity = trajectory_->eval(u, 1)[0];
                state.acceleration = trajectory_->eval(u, 2)[0];
                state.timestamp = t;
                state.valid = true;

                states.push_back(state);
                timestamps.push_back(t);
            }

            // Store in trajectory buffer
            this->trajectory_buffer_.setTrajectory(states, timestamps);
            return true;

        } catch (const std::exception& e) {
            this->setError("Failed to sample trajectory: " + std::string(e.what()));
            return false;
        }
    }

};

#endif // TOPPRA_INTERPOLATOR_H