#!/usr/bin/env python3
"""
手柄与继电器集成示例
演示如何将手柄输入与继电器控制结合
"""

from relay_test import RelayController
import time


class JoystickRelayIntegration:
    """手柄继电器集成控制器"""
    
    def __init__(self):
        self.relay_controller = RelayController()
        self.last_suck_state = 0
        self.last_rot_state = 0
    
    def process_joystick_input(self, suck, rot):
        """
        处理手柄输入并控制继电器
        
        Args:
            suck: 吸合状态 (0 或 1)
            rot: 旋转状态 (0 或 1)
        """
        print(f"接收到手柄输入: suck={suck}, rot={rot}")
        
        # 检查 suck 状态变化
        if suck != self.last_suck_state:
            print(f"suck 状态变化: {self.last_suck_state} -> {suck}")
            if suck == 1:
                print("当 suck=1 时，发送 RelayController('suck', 1)")
                result = self.relay_controller.control("suck", 1)
                print(f"继电器2=1, 结果: {result['success']}")
            else:
                print("当 suck=0 时，发送 RelayController('suck', 0)")
                result = self.relay_controller.control("suck", 0)
                print(f"继电器2=0, 结果: {result['success']}")
            self.last_suck_state = suck
        
        # 检查 rot 状态变化
        if rot != self.last_rot_state:
            print(f"rot 状态变化: {self.last_rot_state} -> {rot}")
            if rot == 1:
                print("当 rot=1 时，发送 RelayController('rot', 1)")
                result = self.relay_controller.control("rot", 1)
                print(f"继电器1=1, 结果: {result['success']}")
            else:
                print("当 rot=0 时，发送 RelayController('rot', 0)")
                result = self.relay_controller.control("rot", 0)
                print(f"继电器1=0, 结果: {result['success']}")
            self.last_rot_state = rot
    
    def close(self):
        """关闭连接"""
        self.relay_controller.close()


def simulate_joystick_input():
    """模拟手柄输入"""
    print("=== 模拟手柄输入与继电器控制 ===")
    
    integration = JoystickRelayIntegration()
    
    try:
        # 模拟一系列手柄输入
        joystick_inputs = [
            (0, 0),  # 初始状态
            (1, 0),  # 按下 suck 按钮
            (1, 1),  # 同时按下 suck 和 rot
            (0, 1),  # 松开 suck，保持 rot
            (0, 0),  # 全部松开
            (1, 0),  # 再次按下 suck
            (0, 0),  # 松开
        ]
        
        for i, (suck, rot) in enumerate(joystick_inputs):
            print(f"\n--- 输入序列 {i+1} ---")
            integration.process_joystick_input(suck, rot)
            time.sleep(1)  # 模拟时间间隔
        
        print("\n=== 模拟完成 ===")
        
    finally:
        integration.close()


def direct_control_example():
    """直接控制示例"""
    print("\n=== 直接控制示例 ===")
    
    controller = RelayController()
    
    try:
        # 直接的控制调用
        commands = [
            ("suck", 1),
            ("rot", 1),
            ("suck", 0),
            ("rot", 0),
        ]
        
        for action, state in commands:
            print(f"发送: RelayController('{action}', {state})")
            result = controller.control(action, state)
            relay = controller.control_mapping[action]
            print(f"  -> 继电器{relay}={state}, 成功: {result['success']}")
            time.sleep(0.5)
    
    finally:
        controller.close()


def button_press_simulation():
    """按钮按下模拟"""
    print("\n=== 按钮按下模拟 ===")
    
    controller = RelayController()
    
    try:
        print("模拟按钮按下和松开:")
        
        # 模拟 suck 按钮按下
        print("\n1. suck 按钮按下")
        suck_pressed = 1
        print(f"   当 suck={suck_pressed} 时，发送 RelayController('suck', {suck_pressed})")
        result = controller.control("suck", suck_pressed)
        print(f"   结果: {result['success']}")
        
        time.sleep(2)
        
        # 模拟 suck 按钮松开
        print("\n2. suck 按钮松开")
        suck_released = 0
        print(f"   当 suck={suck_released} 时，发送 RelayController('suck', {suck_released})")
        result = controller.control("suck", suck_released)
        print(f"   结果: {result['success']}")
        
        time.sleep(1)
        
        # 模拟 rot 按钮按下
        print("\n3. rot 按钮按下")
        rot_pressed = 1
        print(f"   当 rot={rot_pressed} 时，发送 RelayController('rot', {rot_pressed})")
        result = controller.control("rot", rot_pressed)
        print(f"   结果: {result['success']}")
        
        time.sleep(2)
        
        # 模拟 rot 按钮松开
        print("\n4. rot 按钮松开")
        rot_released = 0
        print(f"   当 rot={rot_released} 时，发送 RelayController('rot', {rot_released})")
        result = controller.control("rot", rot_released)
        print(f"   结果: {result['success']}")
    
    finally:
        controller.close()


if __name__ == "__main__":
    try:
        simulate_joystick_input()
        direct_control_example()
        button_press_simulation()
        
    except Exception as e:
        print(f"运行出错: {e}")
        print("请确保继电器服务器正在运行 (tcp://localhost:5555)")
