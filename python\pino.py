#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import numpy as np
import pinocchio as pin
# import matplotlib.pyplot as plt
# from mpl_toolkits.mplot3d import Axes3D
# from multiprocessing import Pool



class RobKmSolver:
    def __init__(self, urdf_path, ee_link_name="ee_link", verbose=True):
        """
        初始化机器人运动学类(优化版)
        
        参数:
            urdf_path: URDF文件的路径
            ee_link_name: 末端执行器链接名称
            verbose: 是否打印详细信息
        """
        # 加载URDF模型
        self.model = pin.buildModelFromUrdf(urdf_path)
        self.data = self.model.createData()
        self.verbose = verbose
        
        # 获取关节限位信息
        self.joint_limits = []
        for i in range(1, self.model.njoints):  # 跳过根关节
            joint_name = self.model.names[i]
            joint_id = self.model.getJointId(joint_name)
            
            # 获取关节类型
            joint_type = self.model.joints[joint_id].shortname()
            
            # 获取关节限位
            if self.model.lowerPositionLimit[i-1] != -float('inf') and self.model.upperPositionLimit[i-1] != float('inf'):
                lower_limit = self.model.lowerPositionLimit[i-1]
                upper_limit = self.model.upperPositionLimit[i-1]
            else:
                # 如果URDF没有定义限位，为旋转关节设置[-π, π]的默认限位
                if joint_type in ['JointModelRX', 'JointModelRY', 'JointModelRZ']:
                    lower_limit = -np.pi
                    upper_limit = np.pi
                else:
                    lower_limit = -float('inf')
                    upper_limit = float('inf')
            
            self.joint_limits.append((lower_limit, upper_limit))
            
        if verbose:
            # 打印关节限位信息
            print("关节限位:")
            for i, (lower, upper) in enumerate(self.joint_limits):
                print(f"  关节 {i+1}: [{lower:.2f}, {upper:.2f}]")

            # 输出模型信息
            print("成功加载模型:")
            print(f"- 自由度: {self.model.nq}")
            print(f"- 关节数: {self.model.njoints - 1}")  # 减去根关节
        
        # 存储末端执行器ID
        try:
            self.ee_id = self.model.getFrameId(ee_link_name)
            if verbose:
                print(f"找到末端执行器: {ee_link_name} (ID: {self.ee_id})")
        except Exception as e:
            if verbose:
                print(f"警告: 无法找到指定的末端执行器 '{ee_link_name}'")
                print("可用的frame名称:")
                for i, frame in enumerate(self.model.frames):
                    print(f"  {i}: {frame.name}")
            
            # 使用最后一个连杆作为末端执行器
            self.ee_id = self.model.nframes - 1
            if verbose:
                print(f"使用最后一个frame作为末端执行器: {self.model.frames[self.ee_id].name} (ID: {self.ee_id})")
        
        # 预计算姿态优化的权重
        self._compute_posture_weights()
    
    def _compute_posture_weights(self):
        """
        预计算用于姿态优化的关节权重
        """
        # 基于关节限位范围计算权重 - 活动范围大的关节权重更小
        weights = np.ones(self.model.nq)
        for i, (lower, upper) in enumerate(self.joint_limits):
            joint_range = upper - lower
            # 避免除以零或极小值
            if joint_range > 1e-6:
                # 权重与活动范围成反比
                weights[i] = 1.0 / max(joint_range, 0.1)
            else:
                weights[i] = 10.0  # 活动范围很小的关节设置较大权重
        
        # 归一化权重
        if np.sum(weights) > 0:
            weights /= np.sum(weights)
        
        self.posture_weights = weights
        
    def forward_kinematics(self, q):
        """
        计算正向运动学
        
        参数:
            q: 关节角度向量 (np.ndarray)
            
        返回:
            ee_position: 末端执行器位置 (np.ndarray)
            ee_rotation: 末端执行器旋转矩阵 (np.ndarray)
        """
        # 确保q维度正确
        if len(q) != self.model.nq:
            q = np.concatenate([np.zeros(self.model.nq - len(q)), q])
        
        # 计算前向运动学
        pin.forwardKinematics(self.model, self.data, q)
        pin.updateFramePlacements(self.model, self.data)
        
        # 获取末端执行器的位置和旋转
        ee_pose = self.data.oMf[self.ee_id]
        ee_position = ee_pose.translation
        ee_rotation = ee_pose.rotation
        
        return ee_position, ee_rotation
    
    def inverse_kinematics_optimized(self, target_position, target_rotation=None, 
                                    q_init=None, max_iter=1000, eps=1e-4, 
                                    damping=1e-5, step_alpha=0.5, 
                                    use_multi_start=False, num_starts=5,
                                    use_null_space=True,
                                    debug=False):
        """
        优化版逆运动学求解，包含多起点尝试，零空间优化等功能
        
        参数:
            target_position: 目标位置 (np.ndarray)
            target_rotation: 目标旋转矩阵 (np.ndarray, 可选)
            q_init: 初始关节角度 (np.ndarray, 可选)
            max_iter: 最大迭代次数
            eps: 收敛阈值
            damping: 阻尼因子，用于奇异值处理
            step_alpha: 步长因子 (0-1之间)
            use_multi_start: 是否使用多起点求解
            num_starts: 多起点数量
            use_null_space: 是否在零空间优化关节配置
            debug: 是否打印调试信息
            
        返回:
            q_sol: 最佳关节角度
            success: 是否成功找到解
            error: 最终误差
        """
        start_time = time.time()
        
        # 如果启用多起点求解
        if use_multi_start:
            if debug:
                print(f"正在使用{num_starts}个起点进行并行求解...")
            
            # 生成多个起点
            q_inits = []
            
            # 如果用户提供了初始点，以它为第一个起点
            if q_init is not None:
                q_inits.append(q_init)
            
            # 生成额外的随机起点，覆盖解空间
            while len(q_inits) < num_starts:
                # 在关节限位内随机生成
                random_q = np.zeros(self.model.nq)
                for i, (lower, upper) in enumerate(self.joint_limits):
                    random_q[i] = np.random.uniform(lower, upper)
                q_inits.append(random_q)
            
            # 并行处理多起点
            results = []
            for i, start_q in enumerate(q_inits):
                if debug:
                    print(f"求解起点 {i+1}/{len(q_inits)}...")
                
                # 对每个起点运行基本IK求解
                q, success, error, _ = self._inverse_kinematics_base(
                    target_position, target_rotation, start_q,
                    max_iter, eps, damping, step_alpha, 
                    use_null_space=use_null_space,
                    debug=debug
                )
                
                results.append((q, success, error))
            
            # 选择最佳解（成功且误差最小）
            successful_results = [(q, err) for q, succ, err in results if succ]
            if successful_results:
                # 从成功解中选择误差最小的
                best_q, best_error = min(successful_results, key=lambda x: x[1])
                success = True
            else:
                # 如果没有成功解，选择误差最小的
                best_q, success, best_error = min(results, key=lambda x: x[2])
            
            if debug:
                print(f"多起点求解完成，找到 {len(successful_results)}/{len(results)} 个成功解")
                print(f"最佳误差: {best_error:.6f}")
            
            q_sol = best_q
            error = best_error
            
        else:
            # 单起点求解
            if q_init is None:
                # 生成初始点 - 选择关节中位值
                q_init = np.zeros(self.model.nq)
                for i, (lower, upper) in enumerate(self.joint_limits):
                    q_init[i] = (lower + upper) / 2
            
            # 运行基本IK求解
            q_sol, success, error, _ = self._inverse_kinematics_base(
                target_position, target_rotation, q_init,
                max_iter, eps, damping, step_alpha,
                use_null_space=use_null_space,
                debug=debug
            )
        
        # 计算运行时间
        end_time = time.time()
        if debug:
            print(f"求解完成，耗时: {end_time - start_time:.3f} 秒")
        
        return q_sol, success, error
    
    def _inverse_kinematics_base(self, target_position, target_rotation=None, q_init=None, 
                                max_iter=1000, eps=1e-4, damping=1e-5, step_alpha=0.5,
                                use_null_space=True, debug=False):
        """
        基础逆运动学求解器，优化实现
        """
        # 如果没有提供初始关节角度，则初始化为零
        if q_init is None:
            q_init = np.zeros(self.model.nq)
        
        # 复制目标位置和旋转以避免修改原始数据
        t_position = np.array(target_position, dtype=np.float64).copy()
        t_rot = np.array(target_rotation, dtype=np.float64).copy() if target_rotation is not None else None
        
        # 复制初始关节角度作为解的起点
        q_sol = np.array(q_init, dtype=np.float64).copy()
        
        if debug:
            print(f"目标位置: {t_position}")
            if t_rot is not None:
                print(f"目标旋转矩阵:\n{t_rot}")
            print(f"初始关节角度 (deg): {np.degrees(q_sol)}")
        
        # 初始化状态变量
        success = False
        error = float('inf')
        prev_error = float('inf')
        stagnation_count = 0
        iterations = 0
        
        # 自适应阻尼因子
        current_damping = damping
        
        # 零空间优化的参考姿态 - 优先选择关节中位值
        q_mid = np.zeros_like(q_sol)
        for i, (lower, upper) in enumerate(self.joint_limits):
            q_mid[i] = (lower + upper) / 2
        
        # 主循环：最多迭代max_iter次
        for i in range(max_iter):
            iterations = i + 1
            
            # 计算正运动学并更新所有的frame位姿
            pin.forwardKinematics(self.model, self.data, q_sol)
            pin.updateFramePlacements(self.model, self.data)
            
            # 获取当前末端执行器的位置和旋转
            current_pos = self.data.oMf[self.ee_id].translation.copy()
            current_rot = self.data.oMf[self.ee_id].rotation.copy()
            
            # 计算位置误差
            position_error = np.linalg.norm(t_position - current_pos)
            
            # 计算总误差（包括位置误差和可选的旋转误差）
            if t_rot is not None:
                # 使用 log3 计算旋转误差
                rotation_error_vector = pin.log3(t_rot @ current_rot.T)
                rotation_error = np.linalg.norm(rotation_error_vector)
                error = position_error + rotation_error
                
                if debug and (i % 100 == 0 ):
                    print(f"迭代 {iterations}, 位置误差: {position_error:.6f}, 旋转误差: {rotation_error:.6f}")
            else:
                error = position_error
                if debug and (i % 100 == 0 ):
                    print(f"迭代 {iterations}, 位置误差: {position_error:.6f}")
            
            # 检查收敛条件
            if error < eps:
                success = True
                if debug:
                    print(f"成功收敛! 最终误差: {error:.6f}")
                break
            
            # 检测算法是否停滞（连续多次误差变化很小）
            if abs(prev_error - error) < eps * 0.01:
                stagnation_count += 1
                if stagnation_count > 5:
                    # 尝试增加阻尼因子以摆脱局部极小
                    if current_damping < damping * 100:
                        current_damping *= 10
                        stagnation_count = 0
                        if debug:
                            print(f"检测到停滞，增加阻尼因子至 {current_damping}")
                    else:
                        if debug:
                            print(f"算法停滞，终止迭代。最终误差: {error:.6f}")
                        break
            else:
                # 误差在减小，可以逐渐减小阻尼
                if stagnation_count > 0:
                    stagnation_count = 0
                    current_damping = max(damping, current_damping * 0.5)
            
            prev_error = error
            
            # 获取末端执行器的雅可比矩阵 - 使用效率更高的computeFrameJacobian
            J = pin.computeFrameJacobian(self.model, self.data, q_sol, self.ee_id, pin.ReferenceFrame.LOCAL_WORLD_ALIGNED)
            
            # 构造任务空间误差向量和对应的雅可比矩阵
            if t_rot is None:
                # 仅考虑位置误差
                err = t_position - current_pos
                J_task = J[:3, :]  # 只使用位置部分的雅可比矩阵
            else:
                # 同时考虑位置和旋转误差
                err = np.concatenate([t_position - current_pos, rotation_error_vector])
                J_task = J  # 使用完整的雅可比矩阵（位置和旋转）
            
            # 使用阻尼最小二乘法求解关节更新量
            J_square = J_task.T @ J_task
            damping_matrix = current_damping * np.eye(J_square.shape[0])
            
            # 使用更效率的求解方法，避免直接求逆
            dq_task = np.linalg.solve(J_square + damping_matrix, J_task.T @ err)
            
            # 零空间投影矩阵计算
            if use_null_space:
                # 计算投影矩阵 (I - J# * J)
                J_pinv = np.linalg.solve(J_square + damping_matrix, J_task.T)
                null_proj = np.eye(self.model.nv) - J_pinv @ J_task
                
                # 零空间任务: 尽量靠近理想姿态 q_mid
                posture_err = self.posture_weights * (q_mid - q_sol)
                dq_null = null_proj @ posture_err
                
                # 总更新 = 任务空间更新 + 零空间更新
                dq = dq_task + 0.1 * dq_null
            else:
                dq = dq_task
            
            # 自适应步长：基于误差和收敛状态
            if error > prev_error and i > 10:
                # 如果误差增加，减小步长
                adaptive_step = step_alpha * 0.5
            else:
                # 正常自适应步长
                adaptive_step = step_alpha * min(1.0, 1.0 / (1.0 + error))
            
            # 更新关节角度
            q_sol += adaptive_step * dq
            
            # 应用关节限位和归一化
            q_sol = self._normalize_and_limit_joints(q_sol)
        
        if debug:
            if success:
                print(f"IK求解成功! 迭代次数: {iterations}")
            else:
                print(f"IK未收敛。迭代次数: {iterations}, 最终误差: {error:.6f}")
        
        return q_sol, success, error, iterations
    
    def _normalize_and_limit_joints(self, q):
        """
        归一化关节角度并应用关节限位
        """
        normalized_q = q.copy()
        
        for i in range(len(normalized_q)):
            # 获取关节类型
            joint_id = i + 1  # 跳过根关节
            if joint_id >= len(self.model.joints):
                continue
                
            joint_type = self.model.joints[joint_id].shortname()
            
            # 对旋转关节进行角度归一化
            if joint_type in ['JointModelRX', 'JointModelRY', 'JointModelRZ']:
                # 将角度归一化到[-π, π]范围
                normalized_q[i] = ((normalized_q[i] + np.pi) % (2 * np.pi)) - np.pi
            
            # 应用关节限位
            if i < len(self.joint_limits):
                lower, upper = self.joint_limits[i]
                normalized_q[i] = np.clip(normalized_q[i], lower, upper)
        
        return normalized_q
    
    # ## 未用到
    # def manipulability_index(self, q):
    #     """
    #     计算当前构型的可操作度指标
        
    #     参数:
    #         q: 关节角度向量
            
    #     返回:
    #         w: 可操作度指标 (越大表示越灵活)
    #     """
    #     # 更新关节位置
    #     pin.forwardKinematics(self.model, self.data, q)
    #     pin.updateFramePlacements(self.model, self.data)
        
    #     # 计算雅可比矩阵
    #     J = pin.computeFrameJacobian(self.model, self.data, q, self.ee_id, pin.ReferenceFrame.LOCAL_WORLD_ALIGNED)
        
    #     # 计算位置雅可比矩阵
    #     J_pos = J[:3, :]
        
    #     # 计算可操作度 (Yoshikawa manipulability)
    #     w = np.sqrt(np.linalg.det(J_pos @ J_pos.T))
        
    #     return w
    
    ## 未用到
    # def find_optimal_posture(self, target_position, target_rotation=None, n_samples=20, debug=False):
    #     """
    #     寻找最优姿态 - 通过采样不同初始位姿并优化可操作度
        
    #     参数:
    #         target_position: 目标位置
    #         target_rotation: 目标旋转矩阵 (可选)
    #         n_samples: 采样数量
    #         debug: 是否打印调试信息
            
    #     返回:
    #         best_q: 最优关节角度
    #         success: 是否找到可行解
    #     """
    #     if debug:
    #         print(f"正在寻找最优姿态, 采样 {n_samples} 个初始位置...")
        
    #     best_q = None
    #     best_manip = -float('inf')
    #     found_solution = False
        
    #     # 生成采样初始位置
    #     samples = []
        
    #     # 在关节空间中采样
    #     for _ in range(n_samples):
    #         q_sample = np.zeros(self.model.nq)
    #         for i, (lower, upper) in enumerate(self.joint_limits):
    #             # 偏向关节中位值的采样
    #             mid = (lower + upper) / 2
    #             # 在中间值附近的较窄范围内采样，以增加有效解的概率
    #             width = (upper - lower) * 0.7  # 70%的关节范围
    #             q_sample[i] = np.random.uniform(max(lower, mid - width/2), min(upper, mid + width/2))
    #         samples.append(q_sample)
        
    #     # 测试每个采样位置
    #     for i, q_init in enumerate(samples):
    #         if debug and (i % 5 == 0):
    #             print(f"测试样本 {i+1}/{n_samples}...")
            
    #         # 运行IK求解
    #         q_sol, success, error = self.inverse_kinematics_optimized(
    #             target_position, 
    #             target_rotation, 
    #             q_init=q_init,
    #             max_iter=500,  # 减少每个样本的迭代次数
    #             eps=1e-3,      # 略微放宽收敛条件
    #             debug=False
    #         )
            
    #         # 如果求解成功，计算可操作度
    #         if success:
    #             found_solution = True
    #             manip = self.manipulability_index(q_sol)
                
    #             # 检查是否是最佳解
    #             if manip > best_manip:
    #                 best_manip = manip
    #                 best_q = q_sol.copy()
    #                 if debug:
    #                     print(f"发现更好的解: 可操作度 = {manip:.4f}")
        
    #     if debug:
    #         if found_solution:
    #             print(f"最优位形找到! 最佳可操作度: {best_manip:.4f}")
    #         else:
    #             print("未找到可行解")
        
    #     return best_q, found_solution
    

def main():
    """
    主函数 - 运行优化版运动学测试
    """
    # URDF文件路径 (修改为您的URDF文件路径)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    urdf_path = os.path.join(current_dir, "../config/truss/urdf/aubo.urdf")  # 假设使用Aubo机器人
    
    # 如果文件不存在，提示用户
    if not os.path.exists(urdf_path):
        print(f"找不到URDF文件: {urdf_path}")
        print("请更新代码中的URDF文件路径")
        return
    
    # 创建测试实例
    robot = RobKmSolver(urdf_path, ee_link_name="wrist3_Link")
    
    # 测试正向运动学
    q_test = np.array([10.0, 20.0, 30.0, 40.0, 50.0, 60.0]) * np.pi/180.0
    target_pos, target_rot = robot.forward_kinematics(q_test)
    print("\n=== 正向运动学测试 ===")
    print(f"输入关节角度 (度): {q_test * 180 / np.pi}")
    print(f"末端位置: {target_pos}")
    
    print("\n=== 基础逆运动学测试 ===")
    # 稍微修改目标，测试逆解能力
    modified_target = target_pos #+ np.array([0.05, 0.02, 0.01])
    q_init = q_test + np.random.uniform(-0.1, 0.1, len(q_test))  # 随机初始角度
    q_sol, success, error = robot.inverse_kinematics_optimized(
        modified_target, target_rot, debug=True,q_init = q_init
    )
    print(f"基础IK求解 {'成功' if success else '失败'}, 误差: {error:.6f}")
    print(f"解得关节角度 (度): {q_sol * 180 / np.pi}")
    
    # print("\n=== 最优位形求解测试 ===")
    # # 寻找最优位形
    # best_q, found = robot.find_optimal_posture(
    #     modified_target, target_rot, n_samples=10, debug=True
    # )
    # if found:
    #     print(f"最优位形关节角度 (度): {best_q * 180 / np.pi}")
    #     # 计算可操作度指标
    #     manip = robot.manipulability_index(best_q)
    #     print(f"最优位形可操作度: {manip:.4f}")
        
    #     # 比较与基本解的可操作度
    #     basic_manip = robot.manipulability_index(q_sol)
    #     print(f"基础解可操作度: {basic_manip:.4f}")
    #     print(f"改进比例: {(manip/basic_manip - 1)*100:.1f}%")
        
    #     # 验证解的正确性
    #     pos, rot = robot.forward_kinematics(best_q)
    #     pos_error = np.linalg.norm(modified_target - pos)
    #     rot_error = np.linalg.norm(target_rot - rot) if target_rot is not None else 0
    #     print(f"位置误差: {pos_error:.6f}, 旋转误差: {rot_error:.6f}")
    

if __name__ == "__main__":
    main()