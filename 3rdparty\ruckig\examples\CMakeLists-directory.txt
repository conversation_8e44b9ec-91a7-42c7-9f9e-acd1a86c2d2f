# A CMakeLists.txt to show how to use Ruckig with CMake when library and headers are in a known directory.

cmake_minimum_required(VERSION 3.10)

project(ruckig_examples)

include_directories(include)
link_directories(lib)

# Build the position example
add_executable(example-position 01_position.cpp)
target_compile_features(example-position PUBLIC cxx_std_17)

target_link_libraries(example-position ruckig)