#ifndef RUCKIG_INTERPOLATOR_H
#define RUCKIG_INTERPOLATOR_H

#include "TrajInterpolatorBase.h"
#include <ruckig/ruckig.hpp>
#include <Eigen/Dense>
#include <vector>
#include <iostream>
#include <cmath>

using namespace ruckig;

template <const int N>
class RuckigInterpolator : public TrajInterpolator<N> {
public:
    using typename TrajInterpolator<N>::VectorNd;
    using typename TrajInterpolator<N>::MatrixNd;

    RuckigInterpolator(double control_period, const std::string& urdf_path)
        : TrajInterpolator<N>(control_period, urdf_path), m_otg(control_period) {
            this->m_dt = control_period;
            m_input.minimum_duration = this->m_dt;
        }

    void computeOffline(const std::vector<VectorNd>&) override {}

    bool computeOnline(const VectorNd& current_position,
                       const VectorNd& current_velocity,
                       const VectorNd& current_acceleration,
                       const VectorNd& target_position,
                       const VectorNd& target_velocity,
                       const VectorNd& target_acceleration,
                       double time_step) override {
        // 设置输入参数
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.target_position.data()) = target_position;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.target_velocity.data()) = target_velocity;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.target_acceleration.data()) = target_acceleration;

        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.current_position.data()) = current_position;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.current_velocity.data()) = current_velocity;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.current_acceleration.data()) = current_acceleration;

        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.max_velocity.data()) = this->m_max_vel;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.max_acceleration.data()) = this->m_max_acc;
        Eigen::Map<Eigen::Matrix<double, N, 1>>(m_input.max_jerk.data()) = this->m_max_jerk;

        // 在线步进
        while (m_otg.update(m_input, m_output) == ruckig::Result::Working) {
            m_output.pass_to_input(m_input);
        }
        std::cout << "t2 : " << m_output.trajectory.get_duration() <<" ms" << std::endl;
        m_trajectory = m_output.trajectory;
        //input.duration_discretization = DurationDiscretization::Discrete;
        //Result res = otg.calculate(input, trajectory);//离线规划
        // std::cout << "traj :  [" << trajectory.get_duration()*1000. << " ms]"<<std::endl;
        return false;
    }

    void sample(double dt,
                std::vector<double>& ts,
                MatrixNd& pos,
                MatrixNd& vel,
                MatrixNd& acc) const override {
        double duration = m_trajectory.get_duration();
        int Np = static_cast<int>(std::ceil(duration / dt)) + 1;
        ts.resize(Np);
        pos.resize(Np, N);
        vel.resize(Np, N);
        acc.resize(Np, N);
        std::array<double, N> new_pos, new_vel, new_acc;
        for (int i = 0; i < Np; ++i) {
            double t = std::min(i * dt, duration);
            ts[i] = t;
            m_trajectory.at_time(t, new_pos, new_vel, new_acc);
            pos.row(i) = Eigen::Map<Eigen::Matrix<double, N, 1>>(new_pos.data());
            vel.row(i) = Eigen::Map<Eigen::Matrix<double, N, 1>>(new_vel.data());
            acc.row(i) = Eigen::Map<Eigen::Matrix<double, N, 1>>(new_acc.data());
        }
    }

private:
    Ruckig<N> m_otg;
    InputParameter<N> m_input;
    OutputParameter<N> m_output;
    Trajectory<N> m_trajectory;
};

#endif // RUCKIG_INTERPOLATOR_H 