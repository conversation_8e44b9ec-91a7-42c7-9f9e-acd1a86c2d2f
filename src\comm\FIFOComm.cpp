#include "FIFOComm.h"
#include <iostream>
#include <cstring>
#include <chrono>

FIFOComm::FIFOComm(bool is_server) : is_server_(is_server) {}

FIFOComm::~FIFOComm() {
    close();
}

bool FIFOComm::init() {
    mkfifo(data_stream_fifo, 0666);
    if (is_server_) {
        fd_data_stream_ = open(data_stream_fifo, O_RDONLY | O_NONBLOCK);
    } else {
        fd_data_stream_ = open(data_stream_fifo, O_WRONLY);
    }
    if (fd_data_stream_ == -1) {
        std::cerr << "[FIFO] Failed to open servo FIFO file" << std::endl;
        return false;
    }
    std::cout << "[FIFO] Servo FIFO initialized (is_server=" << is_server_ << ")" << std::endl;
    return true;
}

void FIFOComm::close() {
    if (fd_data_stream_ != -1) ::close(fd_data_stream_);
    fd_data_stream_ = -1;
}

bool FIFOComm::isConnected() const {
    return fd_data_stream_ != -1;
}

bool FIFOComm::sendRtData(const ServoCommand& cmd) {
    // if (fd_data_stream_ == -1) return false;
    // char buf[512];
    // int n = snprintf(buf, sizeof(buf), "M ");
    // // position
    // for (int i = 0; i < 6; ++i) {
    //     n += snprintf(buf+n, sizeof(buf)-n, "%f", cmd.position[i]);//*10000
    //     if (i < 5) n += snprintf(buf+n, sizeof(buf)-n, ",");
    // }
    // n += snprintf(buf+n, sizeof(buf)-n, " ");
    // // velocity
    // for (int i = 0; i < 6; ++i) {
    //     n += snprintf(buf+n, sizeof(buf)-n, "%ld", getCurrentTimestamp());
    //     if (i < 5) n += snprintf(buf+n, sizeof(buf)-n, ",");
    // }
    // n += snprintf(buf+n, sizeof(buf)-n, " ");
    // // time[6]，全部填充为 duration_ms
    // for (int i = 0; i < 6; ++i) {
    //     n += snprintf(buf+n, sizeof(buf)-n, "%u", cmd.duration_ms);
    //     if (i < 5) n += snprintf(buf+n, sizeof(buf)-n, ",");
    // }
    // std::string msg(buf);
    // std::cout << "[FIFO] Send : " << msg << std::endl;
    // ssize_t written = write(fd_data_stream_, msg.c_str(), msg.size());
    // return written == (ssize_t)msg.size();
    if (fd_data_stream_ == -1) return false;
    char buf[128];
    // 这里只取 position[0] 和 duration_ms，格式为 test_pt 1 pos time
    int n = snprintf(buf, sizeof(buf), "test_PT 1 %.6f %u", cmd.position[0], cmd.duration_ms);
    std::string msg(buf);
    std::cout << "[FIFO] Send : " << msg << std::endl;
    ssize_t written = write(fd_data_stream_, msg.c_str(), msg.size());
    return written == (ssize_t)msg.size();
}

bool FIFOComm::recvRtData(const std::function<void(const ServoCommand&)>& callback) {
    if (fd_data_stream_ == -1) return false;
    char buf[512] = {0};
    ssize_t n = read(fd_data_stream_, buf, sizeof(buf)-1);
    if (n <= 0) return false;
    buf[n] = '\0';
    std::cout << "[FIFO] Recv servo : " << buf << std::endl;
    ServoCommand cmd;
    char tag;
    double pos[6], vel[6];
    unsigned int time[6];
    int ret = sscanf(buf, "M %lf %lf %lf %lf %lf %lf %lf %lf %lf %lf %lf %lf %u %u %u %u %u %u",
        &pos[0], &pos[1], &pos[2], &pos[3], &pos[4], &pos[5],
        &vel[0], &vel[1], &vel[2], &vel[3], &vel[4], &vel[5],
        &time[0], &time[1], &time[2], &time[3], &time[4], &time[5]);
    if (ret == 18) {
        for (int i = 0; i < 6; ++i) {
            cmd.position[i] = pos[i];
            cmd.velocity[i] = vel[i];
        }
        // 取第一个 time 作为 duration_ms
        cmd.duration_ms = time[0];
        if (callback) callback(cmd);
        return true;
    }
    return false;
}

ssize_t FIFOComm::read_full(int fd, char* buf, size_t len) {
    ssize_t total_read = 0;
    while (total_read < static_cast<ssize_t>(len)) {
        ssize_t bytes_read = read(fd, buf + total_read, len - total_read);
        if (bytes_read <= 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                break;
            }
            return -1;
        }
        total_read += bytes_read;
    }
    return total_read;
} 

uint64_t FIFOComm::getCurrentTimestamp() const {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()
    ).count();
}