#!/usr/bin/env python3
"""
RelayController 使用示例
演示如何使用 RelayController("suck", 1) 这样的调用方式
"""

from relay_test import RelayController
import time


def main():
    """主函数 - 演示 RelayController 的使用"""
    print("=== RelayController 使用示例 ===")
    
    # 创建 RelayController 实例
    controller = RelayController()
    
    try:
        print("控制映射:")
        print("  'suck' -> 继电器2 (夹爪控制)")
        print("  'rot'  -> 继电器1 (平台控制)")
        print()
        
        # 当 suck 为 1 时，发送 RelayController("suck", 1)
        print("1. 当 suck=1 时，调用 controller.control('suck', 1)")
        result = controller.control("suck", 1)
        print(f"   发送: 继电器2=1")
        print(f"   结果: {result}")
        time.sleep(1)
        
        # 当 suck 为 0 时，发送 RelayController("suck", 0)
        print("\n2. 当 suck=0 时，调用 controller.control('suck', 0)")
        result = controller.control("suck", 0)
        print(f"   发送: 继电器2=0")
        print(f"   结果: {result}")
        time.sleep(1)
        
        # 当 rot 为 1 时，发送 RelayController("rot", 1)
        print("\n3. 当 rot=1 时，调用 controller.control('rot', 1)")
        result = controller.control("rot", 1)
        print(f"   发送: 继电器1=1")
        print(f"   结果: {result}")
        time.sleep(1)
        
        # 当 rot 为 0 时，发送 RelayController("rot", 0)
        print("\n4. 当 rot=0 时，调用 controller.control('rot', 0)")
        result = controller.control("rot", 0)
        print(f"   发送: 继电器1=0")
        print(f"   结果: {result}")
        
        print("\n=== 使用示例完成 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        print("请确保继电器服务器正在运行 (tcp://localhost:5555)")
        
    finally:
        controller.close()


def demo_with_variables():
    """演示使用变量控制"""
    print("\n=== 使用变量控制示例 ===")
    
    controller = RelayController()
    
    try:
        # 模拟从其他地方获取的控制变量
        suck_state = 1  # 假设这是从手柄或其他输入获取的值
        rot_state = 1   # 假设这是从手柄或其他输入获取的值
        
        print(f"当前状态: suck={suck_state}, rot={rot_state}")
        
        # 当 suck 为 1 时，发送 RelayController("suck", 1)
        if suck_state == 1:
            print("检测到 suck=1，发送控制命令...")
            result = controller.control("suck", suck_state)
            print(f"controller.control('suck', {suck_state}) -> {result['success']}")
        
        time.sleep(1)
        
        # 当 rot 为 1 时，发送 RelayController("rot", 1)
        if rot_state == 1:
            print("检测到 rot=1，发送控制命令...")
            result = controller.control("rot", rot_state)
            print(f"controller.control('rot', {rot_state}) -> {result['success']}")
        
        # 改变状态
        suck_state = 0
        rot_state = 0
        
        print(f"\n状态改变: suck={suck_state}, rot={rot_state}")
        
        # 发送新的控制命令
        result1 = controller.control("suck", suck_state)
        result2 = controller.control("rot", rot_state)
        
        print(f"controller.control('suck', {suck_state}) -> {result1['success']}")
        print(f"controller.control('rot', {rot_state}) -> {result2['success']}")
        
    finally:
        controller.close()


def demo_shorthand_methods():
    """演示简化方法"""
    print("\n=== 简化方法示例 ===")
    
    with RelayController() as controller:
        print("也可以使用简化的方法:")
        
        # 等同于 controller.control("suck", 1)
        print("controller.suck(1) - 等同于 controller.control('suck', 1)")
        result = controller.suck(1)
        print(f"结果: {result['success']}")
        
        time.sleep(1)
        
        # 等同于 controller.control("rot", 1)
        print("controller.rot(1) - 等同于 controller.control('rot', 1)")
        result = controller.rot(1)
        print(f"结果: {result['success']}")


if __name__ == "__main__":
    main()
    demo_with_variables()
    demo_shorthand_methods()
