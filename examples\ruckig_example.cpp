#include <iostream>
#include <vector>
#include <iomanip> // For std::setprecision

<<<<<<< HEAD
#include "../include/trajectory/RuckigInterpolator.hpp"
=======
<<<<<<< HEAD:examples/ruckig_example.cpp
#include "../include/trajectory/RuckigInterpolator.hpp"
=======
#include "RuckigInterpolator.h"
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0:example/ruckig_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134

constexpr int DOF = 3;

int main() {
<<<<<<< HEAD
=======
<<<<<<< HEAD:examples/ruckig_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

    // Create RuckigInterpolator with new interface
    RuckigInterpolator<DOF> interpolator(0.01, "");

    // Set motion constraints using new MotionConstraints structure
    MotionConstraints<DOF> constraints;
    constraints.max_velocity = VectorDOF::Constant(1.0);
    constraints.max_acceleration = VectorDOF::Constant(2.0);
    constraints.max_jerk = VectorDOF::Constant(5.0);
    interpolator.setConstraints(constraints);

    // Create motion states using new MotionState structure
    MotionState<DOF> start_state;
    start_state.position << 0.0, 0.0, 0.0;
    start_state.velocity << 0.0, 0.0, 0.0;
    start_state.acceleration << 0.0, 0.0, 0.0;

    MotionState<DOF> target_state;
    target_state.position << 1.0, -0.5, 0.8;
    target_state.velocity << 0.0, 0.0, 0.0;
    target_state.acceleration << 0.0, 0.0, 0.0;

    // Compute trajectory using new interface
    bool success = interpolator.computeOnline(start_state, target_state);

    if (!success) {
        std::cerr << "Failed to compute trajectory: " << interpolator.getLastError() << std::endl;
        return -1;
    }

    std::cout << "Trajectory computation successful!" << std::endl;
    std::cout << "Interpolator type: " << interpolator.getTypeName() << std::endl;
    std::cout << "Trajectory duration: " << interpolator.getDuration() << " seconds" << std::endl;

    // Sample and print trajectory using TrajectoryBuffer's built-in method
    std::cout << "\n";
    const auto& trajectory_buffer = interpolator.getTrajectoryBuffer();

    // Debug information
    std::cout << "Debug: Buffer size = " << trajectory_buffer.size() << std::endl;
    std::cout << "Debug: Buffer empty = " << trajectory_buffer.isEmpty() << std::endl;
    std::cout << "Debug: Duration = " << trajectory_buffer.getDuration() << std::endl;

    trajectory_buffer.sampleAndPrint(0.01, 50, 4);  // dt=0.01s, max 50 samples, 4 decimal places

    return 0;
<<<<<<< HEAD
}
=======
}
=======
    using VectorNd = Eigen::Matrix<double, DOF, 1>;
    using MatrixNd = Eigen::Matrix<double, DOF, Eigen::Dynamic>;

    RuckigInterpolator<DOF> interpolator(0.01, "");
    VectorNd max_vel = VectorNd::Constant(1.0);
    VectorNd max_acc = VectorNd::Constant(2.0);
    VectorNd max_jerk = VectorNd::Constant(5.0);
    interpolator.setConstraint(max_vel, max_acc, max_jerk);

    VectorNd q0, dq0, ddq0, q1, dq1, ddq1;
    q0 << 0.0, 0.0, 0.0;
    dq0 << 0.0, 0.0, 0.0;
    ddq0 << 0.0, 0.0, 0.0;
    q1 << 1.0, -0.5, 0.8;
    dq1 << 0.0, 0.0, 0.0;
    ddq1 << 0.0, 0.0, 0.0;

    interpolator.computeOnline(q0, dq0, ddq0, q1, dq1, ddq1, 0.01);

    std::vector<double> ts;
    MatrixNd pos, vel, acc;
    interpolator.sample(0.01, ts, pos, vel, acc);

     // 设置输出精度
     std::cout << std::fixed << std::setprecision(4);

     // 打印采样结果
     for (size_t i = 0; i < ts.size(); ++i) {
         std::cout << "t=" << ts[i] << ", pos=[" << pos.row(i) << "], vel=[" << vel.row(i) << "], acc=[" << acc.row(i) << "]\n";
     }
    return 0;
} 
>>>>>>> 1afb6504391f3fe85c04e238f12a1491eb7b97a0:example/ruckig_example.cpp
>>>>>>> 3a39ed2b9b4dc4bd266c0d5035f1848fa1cf2134
