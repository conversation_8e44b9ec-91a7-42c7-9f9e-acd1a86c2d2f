<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot name="cartesian_3axis">
  <link name="baselink">
    <inertial>
      <origin xyz="0.403397219275085 -0.00683409749209208 -0.131297576988088" rpy="0 0 0" />
      <mass value="10.7655717113778" />
      <inertia ixx="0.141618609018414" ixy="4.67969908955293E-06" ixz="-2.70560214219755E-05"
               iyy="0.844601052561615" iyz="-9.03079711708069E-06" izz="0.737005625120397" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/baselink.STL" />
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/baselink.STL" />
      </geometry>
    </collision>
  </link>
  <link name="link1">
    <inertial>
      <origin xyz="0.11723185146163 -0.0599559679659481 -0.0495705129217311" rpy="0 0 0" />
      <mass value="2.08888889810894" />
      <inertia ixx="0.0130745850785245" ixy="0.000341834501347638" ixz="9.26201008376963E-06"
               iyy="0.00765496299103699" iyz="-2.33236477712953E-07" izz="0.0203928715711282" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link1.STL" />
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="joint1" type="prismatic">
    <origin xyz="0 0 0" rpy="0 0 0" />
    <parent link="baselink" />
    <child link="link1" />
    <axis xyz="1 0 0" />
    <limit lower="0" upper="0.5" effort="10" velocity="2" />
  </joint>
  <link name="link2">
    <inertial>
      <origin xyz="-0.0437749983543119 0.0404780322637577 -0.0895552123386653" rpy="0 0 0" />
      <mass value="1.10056156150636" />
      <inertia ixx="0.00200797048006147" ixy="-4.20535394924665E-08" ixz="-1.31526245302545E-05"
               iyy="0.00222269080419465" iyz="1.0265895575635E-07" izz="0.000969926960583016" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link2.STL" />
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="joint2" type="prismatic">
    <origin xyz="0.21526 -0.1923 -0.0805" rpy="0 0 0" />
    <parent link="link1" />
    <child link="link2" />
    <axis xyz="0 1 0" />
    <limit lower="0" upper="0.25" effort="10" velocity="1" />
  </joint>
  <link name="link3">
    <inertial>
      <origin xyz="0.0277856095063377 -1.98126715622582E-06 -0.0542607415944127" rpy="0 0 0" />
      <mass value="0.24328537761146" />
      <inertia ixx="0.000212178131341653" ixy="3.09413027577709E-08" ixz="2.10596763234074E-07"
               iyy="0.000121360473425295" iyz="-4.91141911360967E-08" izz="0.000148310394223082" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link3.STL" />
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="joint3" type="prismatic">
    <origin xyz="0.001 0.040077 -0.0925" rpy="0 0 0" />
    <parent link="link2" />
    <child link="link3" />
    <axis xyz="0 0 1" />
    <limit lower="0" upper="0.07" effort="10" velocity="0.5" />
  </joint>
  <link name="tool0">
    <inertial>
      <origin xyz="0.0633414546924096 -0.000240274476670022 -0.0663650283885104" rpy="0 0 0" />
      <mass value="1.36038534532158" />
      <inertia ixx="0.000410169696469975" ixy="-7.38246225060875E-07" ixz="5.32103380980485E-05"
               iyy="0.000301252202633816" iyz="-1.29730751635819E-06" izz="0.000478528647326843" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/tool.STL" />
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://cartesian_robot/meshes/tool.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="tool_joint" type="fixed">
    <origin xyz="0.064 0 -0.1013" rpy="0 0 0" />
    <parent link="link3" />
    <child link="tool0" />
  </joint>
</robot>