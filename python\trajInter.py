import numpy as np
import toppra as ta
import toppra.constraint as constraint
import toppra.algorithm as algo
import matplotlib.pyplot as plt
import time

class TrajectoryInterpolator:
    def __init__(self, dt: float = 0.01):
        self.dt = dt
        self.constraints = []
        self.trajectory = None
        self.path = None
        self.solve_time = None
        self.marker_times = []
        self.ts = None
        self.sampled_pos = None
        self.waypoints = None

    def set_motion_constraints(self, dof: int, vlim: float = 1.0, alim: float = 2.0):
        v_bounds = np.array([[-vlim, vlim]] * dof)
        a_bounds = np.array([[-alim, alim]] * dof)
        self.constraints = [
            constraint.JointVelocityConstraint(v_bounds),
            constraint.JointAccelerationConstraint(a_bounds)
        ]

    def compute(self, waypoints: np.ndarray):
        assert self.constraints, "Please set motion constraints first."

        self.waypoints = waypoints
        self.dof = waypoints.shape[1]
        self.path = ta.SplineInterpolator(np.linspace(0, 1, len(waypoints)), waypoints)

        start_time = time.time()
        instance = algo.TOPPRA(self.constraints, self.path, solver_wrapper="seidel")
        self.trajectory = instance.compute_trajectory(0.0, 0.0)
        self.solve_time = time.time() - start_time

        print(f"[INFO] Trajectory optimization took {self.solve_time*1000:.4f} ms.")

    def sample(self):
        if self.trajectory is None:
            raise RuntimeError("Trajectory not yet computed.")

        start_time = time.time()
        duration = self.trajectory.duration
        ts = np.arange(0, duration, self.dt)
        pos = np.array([self.trajectory.eval(t) for t in ts])
        vel = np.array([self.trajectory.evald(t) for t in ts])
        acc = np.array([self.trajectory.evaldd(t) for t in ts])
        sample_time = time.time() - start_time

        print(f"[INFO] Sampling took {sample_time*1000:.4f} ms.")
        return ts, pos, vel, acc

    def get_position(self, t):
        return self.trajectory.eval(t)

    def get_velocity(self, t):
        return self.trajectory.evald(t)

    def get_acceleration(self, t):
        return self.trajectory.evaldd(t)

    def plot(self):
        self.ts, self.sampled_pos, vel, acc = self.sample()

        # 在采样后匹配最接近的 waypoint 时间
        self.marker_times = []
        for wp in self.waypoints:
            distances = np.linalg.norm(self.sampled_pos - wp, axis=1)
            idx = np.argmin(distances)
            self.marker_times.append(self.ts[idx])

        fig, axs = plt.subplots(3, 1, figsize=(12, 8), sharex=True)
        for i in range(self.sampled_pos.shape[1]):
            axs[0].plot(self.ts, self.sampled_pos[:, i], label=f"Joint {i+1}")
            axs[1].plot(self.ts, vel[:, i], label=f"Joint {i+1}")
            axs[2].plot(self.ts, acc[:, i], label=f"Joint {i+1}")

        for ax in axs:
            for i, t_marker in enumerate(self.marker_times):
                ax.axvline(t_marker, color='k', linestyle='--', alpha=0.6)
                axs[0].text(t_marker, axs[0].get_ylim()[1]*0.95, f"P{i}", rotation=90,
                            verticalalignment='top', horizontalalignment='right', fontsize=8, color='k')

        axs[0].set_ylabel("Position [rad]")
        axs[1].set_ylabel("Velocity [rad/s]")
        axs[2].set_ylabel("Acceleration [rad/s²]")
        axs[2].set_xlabel("Time [s]")

        for ax in axs:
            ax.grid(True)
            ax.legend()
        plt.tight_layout()
        plt.show()


# --- 示例用法 ---
if __name__ == "__main__":
    waypoints = np.array([
        [0.0, 0.0, 0.0, 0.0],
        [1.0, 0.5, 0.3, -0.2],
        [1.5, -0.2, 0.6, 0.2],
        [2.0, 0.0, 0.0, 0.0]
    ])

    interpolator = TrajectoryInterpolator(dt=0.01)
    interpolator.set_motion_constraints(dof=4, vlim=1.0, alim=2.0)
    interpolator.compute(waypoints)
    interpolator.plot()


   